set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_project_wbs_mapping_validation (project_id_param uuid) RETURNS TABLE (
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	total_coverage_percentage numeric,
	total_coverage_amount numeric,
	budget_amount numeric,
	status text,
	coverage_details jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    wbs_item_record record;
    validation_result record;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'viewer') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Loop through all WBS items with budget and get validation results
    FOR wbs_item_record IN
        SELECT wli.wbs_library_item_id, wli.code
        FROM public.wbs_library_item wli
        JOIN public.budget_version_item bvi ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
        JOIN public.project p ON p.active_budget_version_id = bv.budget_version_id
        WHERE p.project_id = project_id_param
            AND bvi.budget_amount > 0
        ORDER BY wli.code
    LOOP
        -- Get validation result for this WBS item
        SELECT * INTO validation_result
        FROM public.validate_wbs_item_coverage(project_id_param, wbs_item_record.wbs_library_item_id);

        -- Return the result
        RETURN QUERY
        SELECT
            validation_result.wbs_library_item_id,
            validation_result.wbs_code,
            validation_result.wbs_description,
            validation_result.total_coverage_percentage,
            validation_result.total_coverage_amount,
            validation_result.budget_amount,
            validation_result.status,
            validation_result.coverage_details;
    END LOOP;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_line_item_coverage (line_item_id_param uuid) RETURNS TABLE (
	is_valid boolean,
	total_coverage_percentage numeric,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_total_coverage numeric(5, 2) := 0;
BEGIN
    -- Calculate total coverage for the line item
    SELECT COALESCE(SUM(coverage_percentage), 0)
    INTO v_total_coverage
    FROM public.tender_wbs_mapping
    WHERE tender_line_item_id = line_item_id_param;

    -- Return validation result
    RETURN QUERY
    SELECT 
        v_total_coverage <= 100 as is_valid,
        v_total_coverage,
        CASE 
            WHEN v_total_coverage > 100 THEN 
                'Total coverage (' || v_total_coverage || '%) exceeds 100%'
            ELSE NULL
        END as error_message;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_project_mapping_completeness (project_id_param uuid) RETURNS TABLE (
	total_wbs_items integer,
	mapped_items integer,
	complete_items integer,
	partial_items integer,
	over_allocated_items integer,
	unmapped_items integer,
	overall_completion_percentage numeric,
	budget_coverage_percentage numeric,
	total_budget_amount numeric,
	total_mapped_amount numeric
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_total_wbs_items integer := 0;
    v_mapped_items integer := 0;
    v_complete_items integer := 0;
    v_partial_items integer := 0;
    v_over_allocated_items integer := 0;
    v_unmapped_items integer := 0;
    v_total_budget_amount numeric(20, 4) := 0;
    v_total_mapped_amount numeric(20, 4) := 0;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'viewer') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Get total budget amount
    SELECT COALESCE(SUM(bvi.budget_amount), 0)
    INTO v_total_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
    JOIN public.project p ON p.active_budget_version_id = bv.budget_version_id
    WHERE p.project_id = project_id_param;

    -- Calculate mapping statistics
    WITH wbs_coverage AS (
        SELECT
            wli.wbs_library_item_id,
            COALESCE(bvi.budget_amount, 0) as budget_amount,
            COALESCE(SUM(twm.coverage_percentage), 0) as total_coverage_percentage,
            COALESCE(SUM(
                CASE
                    WHEN twm.coverage_percentage IS NOT NULL THEN
                        (twm.coverage_percentage / 100.0) * tli.amount
                    WHEN twm.coverage_quantity IS NOT NULL THEN
                        twm.coverage_quantity * tli.unit_rate
                    ELSE 0
                END
            ), 0) as total_mapped_amount,
            COUNT(twm.tender_wbs_mapping_id) as mapping_count
        FROM public.wbs_library_item wli
        LEFT JOIN public.budget_version_item bvi ON bvi.wbs_library_item_id = wli.wbs_library_item_id
        LEFT JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
        LEFT JOIN public.project p ON p.active_budget_version_id = bv.budget_version_id
            AND p.project_id = project_id_param
        LEFT JOIN public.tender_wbs_mapping twm ON twm.wbs_library_item_id = wli.wbs_library_item_id
        LEFT JOIN public.tender_line_item tli ON tli.tender_line_item_id = twm.tender_line_item_id
        LEFT JOIN public.tender_revision tr ON tr.tender_revision_id = tli.tender_revision_id
        LEFT JOIN public.tender t ON t.tender_id = tr.tender_id AND t.project_id = project_id_param
        WHERE bvi.budget_amount IS NOT NULL AND bvi.budget_amount > 0
        GROUP BY wli.wbs_library_item_id, bvi.budget_amount
    )
    SELECT 
        COUNT(*),
        COUNT(*) FILTER (WHERE mapping_count > 0),
        COUNT(*) FILTER (WHERE total_coverage_percentage = 100),
        COUNT(*) FILTER (WHERE total_coverage_percentage > 0 AND total_coverage_percentage < 100),
        COUNT(*) FILTER (WHERE total_coverage_percentage > 100),
        COUNT(*) FILTER (WHERE mapping_count = 0 OR total_coverage_percentage = 0),
        COALESCE(SUM(total_mapped_amount), 0)
    INTO 
        v_total_wbs_items,
        v_mapped_items,
        v_complete_items,
        v_partial_items,
        v_over_allocated_items,
        v_unmapped_items,
        v_total_mapped_amount
    FROM wbs_coverage;

    -- Return results
    RETURN QUERY
    SELECT 
        v_total_wbs_items,
        v_mapped_items,
        v_complete_items,
        v_partial_items,
        v_over_allocated_items,
        v_unmapped_items,
        CASE 
            WHEN v_total_wbs_items > 0 THEN 
                (v_complete_items::numeric / v_total_wbs_items::numeric) * 100
            ELSE 0
        END as overall_completion_percentage,
        CASE 
            WHEN v_total_budget_amount > 0 THEN 
                (v_total_mapped_amount / v_total_budget_amount) * 100
            ELSE 0
        END as budget_coverage_percentage,
        v_total_budget_amount,
        v_total_mapped_amount;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_wbs_item_coverage (
	project_id_param uuid,
	wbs_library_item_id_param uuid
) RETURNS TABLE (
	wbs_library_item_id uuid,
	wbs_code text,
	wbs_description text,
	total_coverage_percentage numeric,
	total_coverage_amount numeric,
	budget_amount numeric,
	status text,
	coverage_details jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    v_budget_amount numeric(20, 4);
    v_total_coverage_percentage numeric(5, 2) := 0;
    v_total_coverage_amount numeric(20, 4) := 0;
    v_status text;
    v_coverage_details jsonb := '[]'::jsonb;
    v_wbs_code text;
    v_wbs_description text;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'viewer') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Get WBS item details and budget amount
    SELECT
        wli.code,
        wli.description,
        COALESCE(bvi.budget_amount, 0)
    INTO
        v_wbs_code,
        v_wbs_description,
        v_budget_amount
    FROM public.wbs_library_item wli
    LEFT JOIN public.budget_version_item bvi ON bvi.wbs_library_item_id = wli.wbs_library_item_id
    LEFT JOIN public.budget_version bv ON bv.budget_version_id = bvi.budget_version_id
    LEFT JOIN public.project p ON p.active_budget_version_id = bv.budget_version_id
        AND p.project_id = project_id_param
    WHERE wli.wbs_library_item_id = wbs_library_item_id_param;

    -- If WBS item not found, return empty result
    IF v_wbs_code IS NULL THEN
        RETURN;
    END IF;

    -- Calculate total coverage and build details
    SELECT 
        COALESCE(SUM(twm.coverage_percentage), 0),
        COALESCE(SUM(
            CASE 
                WHEN twm.coverage_percentage IS NOT NULL THEN 
                    (twm.coverage_percentage / 100.0) * tli.amount
                WHEN twm.coverage_quantity IS NOT NULL THEN 
                    twm.coverage_quantity * tli.unit_rate
                ELSE 0
            END
        ), 0),
        jsonb_agg(
            jsonb_build_object(
                'tender_name', t.name,
                'line_item_description', tli.description,
                'coverage_percentage', twm.coverage_percentage,
                'coverage_amount', 
                CASE 
                    WHEN twm.coverage_percentage IS NOT NULL THEN 
                        (twm.coverage_percentage / 100.0) * tli.amount
                    WHEN twm.coverage_quantity IS NOT NULL THEN 
                        twm.coverage_quantity * tli.unit_rate
                    ELSE 0
                END
            )
        ) FILTER (WHERE twm.tender_wbs_mapping_id IS NOT NULL)
    INTO 
        v_total_coverage_percentage,
        v_total_coverage_amount,
        v_coverage_details
    FROM public.tender_wbs_mapping twm
    JOIN public.tender_line_item tli ON tli.tender_line_item_id = twm.tender_line_item_id
    JOIN public.tender_revision tr ON tr.tender_revision_id = tli.tender_revision_id
    JOIN public.tender t ON t.tender_id = tr.tender_id
    WHERE twm.wbs_library_item_id = wbs_library_item_id_param
        AND t.project_id = project_id_param
        AND tr.is_current = true;

    -- Determine status
    IF v_coverage_details = '[]'::jsonb OR v_total_coverage_percentage = 0 THEN
        v_status := 'unmapped';
    ELSIF v_total_coverage_percentage < 100 THEN
        v_status := 'partial';
    ELSIF v_total_coverage_percentage = 100 THEN
        v_status := 'complete';
    ELSE
        v_status := 'over_allocated';
    END IF;

    -- Return result
    RETURN QUERY
    SELECT 
        wbs_library_item_id_param,
        v_wbs_code,
        v_wbs_description,
        v_total_coverage_percentage,
        v_total_coverage_amount,
        v_budget_amount,
        v_status,
        COALESCE(v_coverage_details, '[]'::jsonb);
END;
$function$;
