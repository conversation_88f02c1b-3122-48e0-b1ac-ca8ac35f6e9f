set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_budget_transfer (
	project_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric,
	reason text,
	line_item_id_param uuid DEFAULT NULL::uuid
) RETURNS TABLE (
	budget_transfer_id uuid,
	is_valid boolean,
	error_message text
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    validation_result record;
    new_transfer_id uuid;
    line_item_project_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Ensure the optional tender line item belongs to the same project
    IF line_item_id_param IS NOT NULL THEN
        SELECT t.project_id
        INTO line_item_project_id
        FROM public.tender_line_item tli
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
        JOIN public.tender t ON tr.tender_id = t.tender_id
        WHERE tli.tender_line_item_id = line_item_id_param;

        IF line_item_project_id IS NULL THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item not found';
            RETURN;
        ELSIF line_item_project_id <> project_id_param THEN
            RETURN QUERY
            SELECT
                NULL::uuid,
                false,
                'Tender line item does not belong to project';
            RETURN;
        END IF;
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid,
            false,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING public.budget_transfer.budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true,
        NULL::text;
END;
$function$;

CREATE OR REPLACE FUNCTION public.validate_budget_transfer (
	project_id_param uuid,
	from_wbs_item_id uuid,
	to_wbs_item_id uuid,
	transfer_amount numeric
) RETURNS TABLE (
	is_valid boolean,
	error_message text,
	from_available_amount numeric,
	to_current_amount numeric,
	existing_transfers numeric
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
    from_budget_amount numeric(20, 4);
    from_transferred_out numeric(20, 4);
    from_transferred_in numeric(20, 4);
    available_amount numeric(20, 4);
    to_budget_amount numeric(20, 4);
    to_transferred_in numeric(20, 4);
    to_transferred_out numeric(20, 4);
    current_to_amount numeric(20, 4);
    existing_transfer_amount numeric(20, 4);
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Basic input validation before looking up budget data
    IF transfer_amount IS NULL THEN
        RETURN QUERY
        SELECT
            false,
            'Transfer amount is required',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    IF from_wbs_item_id = to_wbs_item_id THEN
        RETURN QUERY
        SELECT
            false,
            'Source and target WBS items must be different',
            NULL::numeric(20, 4),
            NULL::numeric(20, 4),
            NULL::numeric(20, 4);
        RETURN;
    END IF;

    -- Get original budget amount for from_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO from_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = from_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Get original budget amount for to_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO to_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = to_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate existing transfers
    SELECT
        COALESCE(SUM(CASE WHEN bt.from_wbs_library_item_id = from_wbs_item_id THEN bt.transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN bt.to_wbs_library_item_id = from_wbs_item_id THEN bt.transfer_amount ELSE 0 END), 0)
    INTO from_transferred_out, from_transferred_in
    FROM public.budget_transfer bt
    WHERE bt.project_id = project_id_param;

    SELECT
        COALESCE(SUM(CASE WHEN bt2.to_wbs_library_item_id = to_wbs_item_id THEN bt2.transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN bt2.from_wbs_library_item_id = to_wbs_item_id THEN bt2.transfer_amount ELSE 0 END), 0)
    INTO to_transferred_in, to_transferred_out
    FROM public.budget_transfer bt2
    WHERE bt2.project_id = project_id_param;

    -- Check for existing transfer between these specific items
    SELECT COALESCE(SUM(bt3.transfer_amount), 0)
    INTO existing_transfer_amount
    FROM public.budget_transfer bt3
    WHERE bt3.project_id = project_id_param
    AND bt3.from_wbs_library_item_id = from_wbs_item_id
    AND bt3.to_wbs_library_item_id = to_wbs_item_id;

    -- Calculate available amount (original budget + transfers in - transfers out)
    available_amount := COALESCE(from_budget_amount, 0) + COALESCE(from_transferred_in, 0) - COALESCE(from_transferred_out, 0);
    current_to_amount := COALESCE(to_budget_amount, 0) + COALESCE(to_transferred_in, 0) - COALESCE(to_transferred_out, 0);

    -- Validate transfer
    RETURN QUERY
    SELECT
        CASE
            WHEN from_budget_amount IS NULL THEN false
            WHEN to_budget_amount IS NULL THEN false
            WHEN transfer_amount <= 0 THEN false
            WHEN transfer_amount > available_amount THEN false
            ELSE true
        END as is_valid,
        CASE
            WHEN from_budget_amount IS NULL THEN 'Source WBS item not found in budget'
            WHEN to_budget_amount IS NULL THEN 'Target WBS item not found in budget'
            WHEN transfer_amount <= 0 THEN 'Transfer amount must be positive'
            WHEN transfer_amount > available_amount THEN 'Transfer amount exceeds available budget'
            ELSE NULL
        END as error_message,
        available_amount as from_available_amount,
        current_to_amount as to_current_amount,
        existing_transfer_amount as existing_transfers;
END;
$function$;
