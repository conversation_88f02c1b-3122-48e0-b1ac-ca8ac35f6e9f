{"name": "project-controls", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint . --concurrency=8", "reset-db": "supabase db reset && supabase gen types typescript --local > src/lib/database.types.ts && pnpm format", "test:e2e": "playwright test", "test:mcp": "playwright test --config=playwright-mcp.config.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test": "npm run test:unit -- --run && npm run test:e2e", "test:unit": "vitest run", "test:server": "vitest --project=server", "test:client": "vitest --project=client", "test:ssr": "vitest --project=ssr"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@eslint/compat": "^1.4.0", "@eslint/js": "^9.36.0", "@faker-js/faker": "^10.0.0", "@lucide/svelte": "^0.544.0", "@playwright/test": "^1.55.1", "@storybook/addon-docs": "9.1.8", "@storybook/addon-svelte-csf": "5.0.8", "@storybook/addon-vitest": "9.1.8", "@storybook/sveltekit": "9.1.8", "@sveltejs/adapter-vercel": "^5.10.2", "@sveltejs/enhanced-img": "^0.8.1", "@sveltejs/kit": "^2.43.2", "@sveltejs/vite-plugin-svelte": "^6.2.0", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.18", "@tailwindcss/vite": "^4.1.13", "@tanstack/table-core": "^8.21.3", "@types/d3": "^7.4.3", "@types/d3-array": "^3.2.2", "@types/d3-color": "^3.1.3", "@types/d3-hierarchy": "^3.1.7", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@types/node": "^24.5.2", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "bits-ui": "^2.11.0", "clsx": "^2.1.1", "d3-array": "^3.2.4", "eslint": "^9.36.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-storybook": "9.1.8", "eslint-plugin-svelte": "^3.12.4", "formsnap": "^2.0.1", "globals": "^16.4.0", "layerchart": "2.0.0-next.27", "mode-watcher": "^1.1.0", "paneforge": "1.0.2", "phosphor-svelte": "^3.0.1", "pino-pretty": "^13.1.1", "playwright": "^1.55.1", "prettier": "^3.6.2", "prettier-plugin-sql": "^0.19.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "pretty-quick": "^4.2.2", "simple-git-hooks": "^2.13.1", "storybook": "9.1.8", "supabase": "^2.45.5", "svelte": "^5.39.5", "svelte-check": "^4.3.2", "svelte-sonner": "^1.0.5", "sveltekit-flash-message": "^2.4.6", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^3.1.1", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "typescript-eslint": "^8.44.1", "vite": "^7.1.7", "vitest": "^3.2.4", "vitest-browser-svelte": "^1.1.0", "zod": "4.0.10"}, "dependencies": {"@internationalized/date": "^3.9.0", "@sentry/sveltekit": "^10.14.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "d3": "^7.9.0", "d3-color": "^3.1.0", "d3-hierarchy": "^3.1.2", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "date-fns": "^4.1.0", "pino": "^9.11.0", "posthog-js": "^1.268.1", "posthog-node": "^5.8.8", "resend": "^6.1.0", "short-uuid": "^5.2.0", "uid": "^2.0.2", "xlsx": "file:vendor/xlsx-0.20.3.tgz"}, "packageManager": "pnpm@10.17.1", "pnpm": {"onlyBuiltDependencies": ["@sentry/cli", "@tailwindcss/oxide", "esbuild", "msw", "sharp", "simple-git-hooks", "supabase", "svelte-preprocess"], "ignoredBuiltDependencies": []}}