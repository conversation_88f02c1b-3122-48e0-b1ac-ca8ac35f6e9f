# Tender Analysis Workflow System - Technical Implementation Plan

## Overview

This document provides a comprehensive technical implementation plan for a tender analysis workflow system that integrates with the existing project controls application. The system enables manual tender input, WBS mapping, budget transfers, scoring, comparison, and conversion to work packages.

## 1. Database Schema Design

### 1.1 Core Tables

#### currency

```sql
CREATE TABLE IF NOT EXISTS "public"."currency" (
    "currency_code" text NOT NULL,
    "symbol" text NOT NULL,
    "symbol_position" text NOT NULL CHECK (symbol_position IN ('left', 'right')),
    "description" text NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "currency_pkey" PRIMARY KEY ("currency_code")
);

-- Insert default currencies
INSERT INTO "public"."currency" ("currency_code", "symbol", "symbol_position", "description") VALUES
('SEK', 'kr', 'right', 'Swedish Krona'),
('USD', '$', 'left', 'US Dollar'),
('EUR', '€', 'left', 'Euro'),
('GBP', '£', 'left', 'British Pound'),
('NOK', 'kr', 'right', 'Norwegian Krone'),
('DKK', 'kr', 'right', 'Danish Krone');
```

#### tender

```sql
CREATE TABLE IF NOT EXISTS "public"."tender" (
    "tender_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "project_id" uuid NOT NULL,
    "vendor_id" uuid NOT NULL,
    "tender_name" text NOT NULL,
    "description" text,
    "submission_date" date,
    "validity_period_days" integer,
    "currency_code" text DEFAULT 'SEK' NOT NULL,
    "status" text DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'selected', 'rejected')),
    "notes" text,
    "created_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_pkey" PRIMARY KEY ("tender_id"),
    CONSTRAINT "tender_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_vendor_id_fkey" FOREIGN KEY ("vendor_id") REFERENCES "public"."vendor" ("vendor_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_currency_code_fkey" FOREIGN KEY ("currency_code") REFERENCES "public"."currency" ("currency_code") ON UPDATE RESTRICT ON DELETE RESTRICT
);
```

#### tender_revision

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_revision" (
    "tender_revision_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "tender_id" uuid NOT NULL,
    "revision_number" integer NOT NULL,
    "revision_date" date NOT NULL,
    "revision_notes" text,
    "is_current" boolean DEFAULT true NOT NULL,
    "created_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_revision_pkey" PRIMARY KEY ("tender_revision_id"),
    CONSTRAINT "tender_revision_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "tender_revision_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
);

-- Partial unique index to ensure only one current revision per tender
CREATE UNIQUE INDEX "tender_current_rev_uniq" ON "public"."tender_revision" ("tender_id") WHERE "is_current" = true;

-- Function to ensure only one current revision per tender
CREATE OR REPLACE FUNCTION "public"."ensure_single_current_revision"()
RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
BEGIN
    -- If setting a revision as current, mark all others as non-current
    IF NEW.is_current = true THEN
        UPDATE public.tender_revision
        SET is_current = false
        WHERE tender_id = NEW.tender_id
        AND tender_revision_id != NEW.tender_revision_id
        AND is_current = true;
    END IF;

    RETURN NEW;
END;
$$;

-- Trigger to maintain single current revision
CREATE TRIGGER "ensure_single_current_revision_trigger"
    BEFORE INSERT OR UPDATE ON "public"."tender_revision"
    FOR EACH ROW
    EXECUTE FUNCTION "public"."ensure_single_current_revision"();
```

#### tender_line_item

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_line_item" (
    "tender_line_item_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "tender_revision_id" uuid NOT NULL,
    "line_number" integer NOT NULL,
    "description" text NOT NULL,
    "quantity" numeric(20, 4),
    "unit" text,
    "material_rate" numeric(20, 4),
    "labor_rate" numeric(20, 4),
    "productivity" numeric(20, 4),
    "unit_rate" numeric(20, 4),
    "subtotal" numeric(20, 4),
    "normalization_type" text CHECK ("normalization_type" IN ('amount', 'percentage')) DEFAULT 'amount',
    "normalization_amount" numeric(20, 4),
    "normalization_percentage" numeric(5, 2),
    "notes" text,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_line_item_pkey" PRIMARY KEY ("tender_line_item_id"),
    CONSTRAINT "tender_line_item_tender_revision_id_fkey" FOREIGN KEY ("tender_revision_id") REFERENCES "public"."tender_revision" ("tender_revision_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "tender_line_item_unique_line_number" UNIQUE ("tender_revision_id", "line_number"),
    CONSTRAINT "tender_line_item_normalization_check" CHECK (
        (normalization_type = 'amount' AND normalization_amount IS NOT NULL) OR
        (normalization_type = 'percentage' AND normalization_percentage IS NOT NULL) OR
        (normalization_amount IS NULL AND normalization_percentage IS NULL)
    )
);
```

#### tender_wbs_mapping

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_wbs_mapping" (
    "tender_wbs_mapping_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "tender_line_item_id" uuid NOT NULL,
    "wbs_library_item_id" uuid NOT NULL,
    "coverage_percentage" numeric(5, 2) NOT NULL DEFAULT 100.00,
    "coverage_quantity" numeric(20, 4),
    "mapping_notes" text,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_wbs_mapping_pkey" PRIMARY KEY ("tender_wbs_mapping_id"),
    CONSTRAINT "tender_wbs_mapping_tender_line_item_id_fkey" FOREIGN KEY ("tender_line_item_id") REFERENCES "public"."tender_line_item" ("tender_line_item_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "tender_wbs_mapping_wbs_library_item_id_fkey" FOREIGN KEY ("wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_wbs_mapping_coverage_percentage_check" CHECK (coverage_percentage > 0 AND coverage_percentage <= 100)
);
```

#### budget_transfer

```sql
CREATE TABLE IF NOT EXISTS "public"."budget_transfer" (
    "budget_transfer_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "project_id" uuid NOT NULL,
    "tender_line_item_id" uuid NOT NULL,
    "from_wbs_library_item_id" uuid NOT NULL,
    "to_wbs_library_item_id" uuid NOT NULL,
    "transfer_amount" numeric(20, 4) NOT NULL,
    "transfer_reason" text,
    "created_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "budget_transfer_pkey" PRIMARY KEY ("budget_transfer_id"),
    CONSTRAINT "budget_transfer_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "budget_transfer_tender_line_item_id_fkey" FOREIGN KEY ("tender_line_item_id") REFERENCES "public"."tender_line_item" ("tender_line_item_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "budget_transfer_from_wbs_library_item_id_fkey" FOREIGN KEY ("from_wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "budget_transfer_to_wbs_library_item_id_fkey" FOREIGN KEY ("to_wbs_library_item_id") REFERENCES "public"."wbs_library_item" ("wbs_library_item_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "budget_transfer_amount_positive" CHECK (transfer_amount > 0)
);
```

### 1.2 Scoring Tables

#### tender_scoring_criteria

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_scoring_criteria" (
    "tender_scoring_criteria_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "project_id" uuid NOT NULL,
    "criteria_name" text NOT NULL,
    "description" text,
    "weight" numeric(5, 2) DEFAULT 1.00,
    "max_score" integer DEFAULT 10,
    "sort_order" integer NOT NULL,
    "is_active" boolean DEFAULT true,
    "created_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_scoring_criteria_pkey" PRIMARY KEY ("tender_scoring_criteria_id"),
    CONSTRAINT "tender_scoring_criteria_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."project" ("project_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_scoring_criteria_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_scoring_criteria_weight_positive" CHECK (weight > 0),
    CONSTRAINT "tender_scoring_criteria_max_score_positive" CHECK (max_score > 0),
    CONSTRAINT "tender_scoring_criteria_unique_sort_order" UNIQUE ("project_id", "sort_order")
);
```

#### tender_score

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_score" (
    "tender_score_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "tender_id" uuid NOT NULL,
    "tender_scoring_criteria_id" uuid NOT NULL,
    "score" numeric(5, 2) NOT NULL,
    "comments" text,
    "scored_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "scored_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_score_pkey" PRIMARY KEY ("tender_score_id"),
    CONSTRAINT "tender_score_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "tender_score_tender_scoring_criteria_id_fkey" FOREIGN KEY ("tender_scoring_criteria_id") REFERENCES "public"."tender_scoring_criteria" ("tender_scoring_criteria_id") ON UPDATE RESTRICT ON DELETE CASCADE,
    CONSTRAINT "tender_score_scored_by_user_id_fkey" FOREIGN KEY ("scored_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_score_score_range" CHECK (score >= 0),
    CONSTRAINT "tender_score_unique_tender_criteria" UNIQUE ("tender_id", "tender_scoring_criteria_id")
);
```

### 1.3 Work Package Integration

#### tender_work_package

```sql
CREATE TABLE IF NOT EXISTS "public"."tender_work_package" (
    "tender_work_package_id" uuid DEFAULT gen_random_uuid() NOT NULL,
    "tender_id" uuid NOT NULL,
    "work_package_name" text NOT NULL,
    "work_package_description" text,
    "conversion_date" date NOT NULL,
    "conversion_notes" text,
    "created_by_user_id" uuid DEFAULT auth.uid() NOT NULL,
    "created_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,
    "updated_at" timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL,

    CONSTRAINT "tender_work_package_pkey" PRIMARY KEY ("tender_work_package_id"),
    CONSTRAINT "tender_work_package_tender_id_fkey" FOREIGN KEY ("tender_id") REFERENCES "public"."tender" ("tender_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_work_package_created_by_user_id_fkey" FOREIGN KEY ("created_by_user_id") REFERENCES "public"."profile" ("user_id") ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "tender_work_package_unique_tender" UNIQUE ("tender_id")
);
```

### 1.4 Indexes and Performance

```sql
-- Performance indexes
CREATE INDEX "currency_is_active_idx" ON "public"."currency" USING btree ("is_active")
WHERE
	is_active = true;

CREATE INDEX "tender_project_id_idx" ON "public"."tender" USING btree ("project_id");

CREATE INDEX "tender_vendor_id_idx" ON "public"."tender" USING btree ("vendor_id");

CREATE INDEX "tender_status_idx" ON "public"."tender" USING btree ("status");

CREATE INDEX "tender_revision_tender_id_idx" ON "public"."tender_revision" USING btree ("tender_id");

CREATE INDEX "tender_revision_is_current_idx" ON "public"."tender_revision" USING btree ("is_current")
WHERE
	is_current = true;

CREATE INDEX "tender_line_item_tender_revision_id_idx" ON "public"."tender_line_item" USING btree ("tender_revision_id");

CREATE INDEX "tender_wbs_mapping_tender_line_item_id_idx" ON "public"."tender_wbs_mapping" USING btree ("tender_line_item_id");

CREATE INDEX "tender_wbs_mapping_wbs_library_item_id_idx" ON "public"."tender_wbs_mapping" USING btree ("wbs_library_item_id");

CREATE INDEX "budget_transfer_project_id_idx" ON "public"."budget_transfer" USING btree ("project_id");

CREATE INDEX "tender_scoring_criteria_project_id_idx" ON "public"."tender_scoring_criteria" USING btree ("project_id");

CREATE INDEX "tender_score_tender_id_idx" ON "public"."tender_score" USING btree ("tender_id");
```

## 2. Row Level Security (RLS) Policies

### 2.1 Currency Table Policies

```sql
-- Enable RLS
ALTER TABLE "public"."currency" ENABLE ROW LEVEL SECURITY;

-- SELECT policy - all authenticated users can view currencies
CREATE POLICY "Authenticated users can view currencies" ON "public"."currency" FOR
SELECT
	TO "authenticated" USING (true);

-- Only service role can modify currencies
CREATE POLICY "Service role can insert currencies" ON "public"."currency" FOR INSERT TO "service_role"
WITH
	CHECK (true);

CREATE POLICY "Service role can update currencies" ON "public"."currency" FOR
UPDATE TO "service_role" USING (true)
WITH
	CHECK (true);

CREATE POLICY "Service role can delete currencies" ON "public"."currency" FOR DELETE TO "service_role" USING (true);
```

### 2.2 Tender Table Policies

```sql
-- Enable RLS
ALTER TABLE "public"."tender" ENABLE ROW LEVEL SECURITY;

-- SELECT policy
CREATE POLICY "Users can view tenders for accessible projects" ON "public"."tender"
FOR SELECT TO "authenticated"
USING (
    "public"."current_user_has_entity_access"('project'::"public"."entity_type", "project_id")
);

-- INSERT policy
CREATE POLICY "Users can create tenders for projects they can edit" ON "public"."tender"
FOR INSERT TO "authenticated"
WITH CHECK (
    "public"."current_user_has_entity_role"(
        'project'::"public"."entity_type",
        "project_id",
        'editor'::"public"."membership_role"
    )
);

-- UPDATE policy
CREATE POLICY "Users can update tenders for projects they can edit" ON "public"."tender"
FOR UPDATE TO "authenticated"
USING (
    "public"."current_user_has_entity_role"(
        'project'::"public"."entity_type",
        "project_id",
        'editor'::"public"."membership_role"
    )
)
WITH CHECK (
    "public"."current_user_has_entity_role"(
        'project'::"public"."entity_type",
        "project_id",
        'editor'::"public"."membership_role"
    )
);

-- DELETE policy
CREATE POLICY "Users can delete tenders for projects they own" ON "public"."tender"
FOR DELETE TO "authenticated"
USING (
    "public"."current_user_has_entity_role"(
        'project'::"public"."entity_type",
        "project_id",
        'owner'::"public"."membership_role"
    )
);
```

### 2.2 Related Table Policies

Similar RLS policies need to be created for:

- `tender_revision` (inherits access through `tender`)
- `tender_line_item` (inherits access through `tender_revision`)
- `tender_wbs_mapping` (inherits access through `tender_line_item`)
- `budget_transfer` (direct project access check)
- `tender_scoring_criteria` (direct project access check)
- `tender_score` (inherits access through `tender`)
- `tender_work_package` (inherits access through `tender`)

## 3. API Endpoints & RPC Functions

### 3.1 Core RPC Functions

#### get_project_tenders

```sql
CREATE OR REPLACE FUNCTION "public"."get_project_tenders"(
    "project_id_param" uuid
) RETURNS TABLE (
    tender_id uuid,
    vendor_id uuid,
    vendor_name text,
    tender_name text,
    description text,
    submission_date date,
    status text,
    current_revision_id uuid,
    current_revision_number integer,
    total_amount numeric(20, 4),
    line_item_count integer,
    wbs_coverage_percentage numeric(5, 2),
    average_score numeric(5, 2),
    weighted_score numeric(5, 2),
    created_at timestamptz,
    updated_at timestamptz
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        t.tender_id,
        t.vendor_id,
        v.name as vendor_name,
        t.tender_name,
        t.description,
        t.submission_date,
        t.status,
        tr.tender_revision_id as current_revision_id,
        tr.revision_number as current_revision_number,
        COALESCE(SUM(tli.subtotal), 0) as total_amount,
        COUNT(tli.tender_line_item_id)::integer as line_item_count,
        -- Calculate WBS coverage percentage
        CASE
            WHEN COUNT(DISTINCT twm.wbs_library_item_id) > 0 THEN
                (COUNT(DISTINCT twm.wbs_library_item_id)::numeric /
                 (SELECT COUNT(*) FROM public.wbs_library_item wli
                  JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
                  JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
                  JOIN public.project p ON bv.project_id = p.project_id
                  WHERE p.project_id = project_id_param AND p.active_budget_version_id = bv.budget_version_id)::numeric) * 100
            ELSE 0
        END as wbs_coverage_percentage,
        -- Calculate average score
        AVG(ts.score) as average_score,
        -- Calculate weighted score
        CASE
            WHEN SUM(tsc.weight) > 0 THEN
                SUM(ts.score * tsc.weight) / SUM(tsc.weight)
            ELSE NULL
        END as weighted_score,
        t.created_at,
        t.updated_at
    FROM public.tender t
    JOIN public.vendor v ON t.vendor_id = v.vendor_id
    LEFT JOIN public.tender_revision tr ON t.tender_id = tr.tender_id AND tr.is_current = true
    LEFT JOIN public.tender_line_item tli ON tr.tender_revision_id = tli.tender_revision_id
    LEFT JOIN public.tender_wbs_mapping twm ON tli.tender_line_item_id = twm.tender_line_item_id
    LEFT JOIN public.tender_score ts ON t.tender_id = ts.tender_id
    LEFT JOIN public.tender_scoring_criteria tsc ON ts.tender_scoring_criteria_id = tsc.tender_scoring_criteria_id
    WHERE t.project_id = project_id_param
    GROUP BY t.tender_id, v.name, tr.tender_revision_id, tr.revision_number
    ORDER BY t.tender_name;
END;
$$;
```

#### get_tender_comparison_data

```sql
CREATE OR REPLACE FUNCTION "public"."get_tender_comparison_data"(
    "project_id_param" uuid
) RETURNS TABLE (
    wbs_library_item_id uuid,
    wbs_code text,
    wbs_description text,
    wbs_level integer,
    parent_item_id uuid,
    budget_amount numeric(20, 4),
    budget_quantity numeric(20, 4),
    budget_unit_rate numeric(20, 4),
    tender_data jsonb
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    WITH wbs_budget AS (
        SELECT
            wli.wbs_library_item_id,
            wli.code as wbs_code,
            wli.description as wbs_description,
            wli.level as wbs_level,
            wli.parent_item_id,
            bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1) as budget_amount,
            bvi.quantity as budget_quantity,
            bvi.unit_rate as budget_unit_rate
        FROM public.wbs_library_item wli
        JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
        JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
        JOIN public.project p ON bv.project_id = p.project_id
        WHERE p.project_id = project_id_param
        AND p.active_budget_version_id = bv.budget_version_id
    ),
    tender_mappings AS (
        SELECT
            twm.wbs_library_item_id,
            jsonb_agg(
                jsonb_build_object(
                    'tender_id', t.tender_id,
                    'vendor_name', v.name,
                    'tender_name', t.tender_name,
                    'line_item_id', tli.tender_line_item_id,
                    'line_description', tli.description,
                    'quantity', tli.quantity,
                    'unit', tli.unit,
                    'unit_rate', tli.unit_rate,
                    'subtotal', tli.subtotal,
                    'coverage_percentage', twm.coverage_percentage,
                    'coverage_quantity', twm.coverage_quantity,
                    'normalization_type', tli.normalization_type,
                    'normalization_amount', tli.normalization_amount,
                    'normalization_percentage', tli.normalization_percentage
                ) ORDER BY v.name, t.tender_name
            ) as tender_data
        FROM public.tender_wbs_mapping twm
        JOIN public.tender_line_item tli ON twm.tender_line_item_id = tli.tender_line_item_id
        JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id AND tr.is_current = true
        JOIN public.tender t ON tr.tender_id = t.tender_id
        JOIN public.vendor v ON t.vendor_id = v.vendor_id
        WHERE t.project_id = project_id_param
        GROUP BY twm.wbs_library_item_id
    )
    SELECT
        wb.wbs_library_item_id,
        wb.wbs_code,
        wb.wbs_description,
        wb.wbs_level,
        wb.parent_item_id,
        wb.budget_amount,
        wb.budget_quantity,
        wb.budget_unit_rate,
        COALESCE(tm.tender_data, '[]'::jsonb) as tender_data
    FROM wbs_budget wb
    LEFT JOIN tender_mappings tm ON wb.wbs_library_item_id = tm.wbs_library_item_id
    ORDER BY wb.wbs_code;
END;
$$;
```

#### calculate_normalization_amount

```sql
CREATE OR REPLACE FUNCTION "public"."calculate_normalization_amount"(
    "line_item_id_param" uuid,
    "normalization_percentage_param" numeric(5,2)
) RETURNS numeric(20,4) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
DECLARE
    total_budget_amount numeric(20,4) := 0;
    calculated_amount numeric(20,4);
BEGIN
    -- Calculate total budget amount for all WBS items mapped to this line item
    SELECT COALESCE(SUM(
        bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1) * (twm.coverage_percentage / 100.0)
    ), 0) INTO total_budget_amount
    FROM public.tender_wbs_mapping twm
    JOIN public.wbs_library_item wli ON twm.wbs_library_item_id = wli.wbs_library_item_id
    JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE twm.tender_line_item_id = line_item_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate normalization amount as percentage of total budget
    calculated_amount := total_budget_amount * (normalization_percentage_param / 100.0);

    RETURN calculated_amount;
END;
$$;
```

## 4. TypeScript Schemas & Types

### 4.1 Zod Schemas

```typescript
// src/lib/schemas/tender.ts
import { z } from 'zod';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';

export const tenderSchema = z.object({
	tender_name: z.string().min(1, 'Tender name is required'),
	description: z.string().optional().nullable(),
	vendor_id: z.uuid('Please select a vendor'),
	submission_date: z.date().optional().nullable(),
	validity_period_days: z.number().int().min(1).max(365).optional().nullable(),
	currency_code: z.string().min(3).max(3).default('SEK'),
	notes: z.string().optional().nullable(),
});

export const tenderLineItemSchema = z
	.object({
		line_number: z.number().int().min(1),
		description: z.string().min(1, 'Description is required'),
		quantity: z.number().positive().optional().nullable(),
		unit: z.string().optional().nullable(),
		material_rate: z.number().min(0).optional().nullable(),
		labor_rate: z.number().min(0).optional().nullable(),
		productivity: z.number().positive().optional().nullable(),
		unit_rate: z.number().min(0).optional().nullable(),
		subtotal: z.number().min(0).optional().nullable(),

		normalization_type: z.enum(['amount', 'percentage']).default('amount'),
		normalization_amount: z.number().optional().nullable(),
		normalization_percentage: z.number().min(0).max(100).optional().nullable(),
		notes: z.string().optional().nullable(),
	})
	.refine(
		(data) => {
			// Validate normalization input based on type
			if (data.normalization_type === 'amount') {
				return data.normalization_amount !== null && data.normalization_amount !== undefined;
			} else if (data.normalization_type === 'percentage') {
				return (
					data.normalization_percentage !== null && data.normalization_percentage !== undefined
				);
			}
			return true;
		},
		{
			message: 'Normalization value is required based on selected type',
			path: ['normalization_amount', 'normalization_percentage'],
		},
	);

export const tenderWbsMappingSchema = z.object({
	wbs_library_item_id: z.uuid('Please select a WBS item'),
	coverage_percentage: z.number().min(0.01).max(100).default(100),
	coverage_quantity: z.number().positive().optional().nullable(),
	mapping_notes: z.string().optional().nullable(),
});

export const tenderScoringCriteriaSchema = z.object({
	criteria_name: z.string().min(1, 'Criteria name is required'),
	description: z.string().optional().nullable(),
	weight: z.number().positive().default(1),
	max_score: z.number().int().positive().default(10),
	sort_order: z.number().int().min(1),
});

export const tenderScoreSchema = z.object({
	tender_scoring_criteria_id: z.uuid(),
	score: z.number().min(0),
	comments: z.string().optional().nullable(),
});

// Type definitions
export type TenderSchema = z.infer<typeof tenderSchema>;
export type TenderLineItemSchema = z.infer<typeof tenderLineItemSchema>;
export type TenderWbsMappingSchema = z.infer<typeof tenderWbsMappingSchema>;
export type TenderScoringCriteriaSchema = z.infer<typeof tenderScoringCriteriaSchema>;
export type TenderScoreSchema = z.infer<typeof tenderScoreSchema>;
```

## 5. SvelteKit Routing Structure

### 5.1 Route Organization

```
src/routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/tenders/
├── +page.svelte                           # Tender list/overview
├── +page.server.ts                        # Load tenders for project
├── new/
│   ├── +page.svelte                       # Create new tender form
│   └── +page.server.ts                    # Handle tender creation
├── [tender_id_short]/
│   ├── +page.svelte                       # Tender detail view
│   ├── +page.server.ts                    # Load tender details
│   ├── edit/
│   │   ├── +page.svelte                   # Edit tender form
│   │   └── +page.server.ts                # Handle tender updates
│   ├── revisions/
│   │   ├── +page.svelte                   # Revision history
│   │   ├── +page.server.ts                # Load revisions
│   │   └── new/
│   │       ├── +page.svelte               # Create new revision
│   │       └── +page.server.ts            # Handle revision creation
│   ├── line-items/
│   │   ├── +page.svelte                   # Manage line items
│   │   ├── +page.server.ts                # Load/update line items
│   │   └── [line_item_id_short]/
│   │       ├── wbs-mapping/
│   │       │   ├── +page.svelte           # WBS mapping interface
│   │       │   └── +page.server.ts        # Handle WBS mappings
│   │       └── edit/
│   │           ├── +page.svelte           # Edit line item
│   │           └── +page.server.ts        # Handle line item updates
│   └── scoring/
│       ├── +page.svelte                   # Tender scoring interface
│       └── +page.server.ts                # Handle scoring
├── comparison/
│   ├── +page.svelte                       # Tender comparison view
│   └── +page.server.ts                    # Load comparison data
├── scoring-criteria/
│   ├── +page.svelte                       # Manage scoring criteria
│   └── +page.server.ts                    # Handle criteria CRUD
└── convert-to-work-packages/
    ├── +page.svelte                       # Convert selected tenders
    └── +page.server.ts                    # Handle conversion
```

The `line_item_id_short` and `tender_id_short` params will use `short-uuid` to convert between a public-url-friendly encoded version and the internal uuid for all internal and database references as in `src/lib/schemas/project.ts`.

## 6. Implementation Checklist

### Phase 1: Database Foundation (Week 1)

- [x] Create database schema file for each tender-related table
- [x] Add RLS policies for all new tables in each of the related schema files
- [x] Create audit tables and triggers following existing patterns
- [x] Add indexes for performance optimization
- [x] Add updated_at triggers
- [x] Create core RPC functions for data retrieval
- [x] Add all new schema files in the correct order to `supabase/config.toml`
- [x] Create a migration file to create all new tables, policies and functions using `PGSSLMODE=disable supabase db diff -f <migration_description>`
- [x] Update TypeScript database types (`pnpm reset-db`)
- [x] Replace use of `currencies` in `vendor` table and zod schema with `currency` table
- [x] Test database schema with sample data

### Phase 2: Core API & Schemas (Week 1-2) ✅ COMPLETED

- [x] Refactor `projectShortId` and `projectUUID` in `src/lib/schemas/project.ts` to use `short-uuid` translator for converting between URLs and database references, not just projects
- [x] Create Zod validation schemas for all tender entities
- [x] Implement TypeScript type definitions
- [x] Create form action functions for tender CRUD operations
- [x] Implement tender revision management logic
- [x] Create line item management functions with flexible normalization input
- [x] Implement normalization calculation RPC function
- [x] Create updateNormalizationAmount form action
- [x] Test using vitest and playwright

### Phase 3: Basic UI Components (Week 2-3) ✅ COMPLETED

- [x] Create tender list page with filtering and search
- [x] Implement tender creation form with vendor selection
- [x] Build tender detail view with revision history
- [x] Create line item management interface with normalization toggle
- [x] Implement normalization input UI (amount vs percentage modes)
- [x] Add real-time normalization calculation display
- [x] Implement basic tender editing functionality
- [x] Add confirmation dialogs for destructive actions

### Phase 4: WBS Mapping System (Week 3-4) ✅ COMPLETED

- [x] Create WBS mapping interface for line items
- [x] Implement coverage percentage/quantity input
- [x] Build category-level mapping with checkbox interface
- [x] Create budget transfer tracking system
- [x] Implement validation for mapping completeness

### Phase 5: Scoring System (Week 4-5)

- [ ] Create scoring criteria management interface
- [ ] Implement tender scoring forms with 0-10 scale
- [ ] Build weighted average calculation system
- [ ] Create scoring history tracking
- [ ] Implement score revision functionality
- [ ] Add scoring summary views

### Phase 6: Comparison & Analysis (Week 5-6)

- [ ] Build tender comparison table with WBS organization
- [ ] Implement price variance highlighting
- [ ] Create coverage percentage validation
- [ ] Add normalization amount tracking
- [ ] Implement WBS filtering with search params
- [ ] Create show/hide toggles for tenders
- [ ] Add export functionality for comparison data

### Phase 7: Work Package Integration (Week 6-7)

- [ ] Create tender selection workflow
- [ ] Implement work package conversion interface
- [ ] Build multi-WBS work package creation
- [ ] Create conversion tracking and audit trail
- [ ] Implement validation for selected tender conversion

### Phase 8: Testing & Polish (Week 7-8)

- [ ] Write unit tests for all schemas and utilities
- [ ] Create integration tests for tender workflows
- [ ] Implement comprehensive error handling
- [ ] Add loading states and user feedback
- [ ] Optimize database queries and performance
- [ ] Create user documentation and help text
- [ ] Conduct user acceptance testing
- [ ] Fix bugs and polish UI/UX

### Phase 9: Advanced Features (Week 8-9)

- [ ] Implement tender document attachment system
- [ ] Add email notifications for tender updates
- [ ] Create tender comparison export to Excel/PDF
- [ ] Implement tender deadline tracking and alerts
- [ ] Add bulk operations for line items
- [ ] Create tender analytics and reporting
- [ ] Implement tender template system

### Phase 10: Deployment & Monitoring (Week 9-10)

- [ ] Deploy to staging environment
- [ ] Conduct performance testing
- [ ] Set up monitoring and logging
- [ ] Create backup and recovery procedures
- [ ] Deploy to production
- [ ] Monitor system performance and user feedback
- [ ] Create maintenance documentation

## 7. Testing Strategy

### 7.1 Unit Tests

- Schema validation tests for all Zod schemas
- Utility function tests for calculations and data transformations
- Normalization calculation tests (percentage to amount conversion)
- Component tests for form validation and user interactions
- Integration tests for normalization UI toggle functionality
- **RPC Function Tests**:
  - `get_project_tenders`: Test data retrieval, filtering, and access control
  - `get_tender_comparison_data`: Test WBS hierarchy and tender data aggregation
  - `calculate_normalization_amount`: Test percentage to amount calculations with various WBS mappings
  - `validate_budget_transfer`: Test validation logic for budget transfers
  - `create_budget_transfer`: Test budget transfer creation and validation
  - `get_project_budget_transfers`: Test budget transfer history retrieval
- **Form Action Tests**:
  - `createTender`: Test happy path and validation error scenarios
  - `updateTenderStatus`: Test status transitions and access control
  - `createLineItem`: Test line item creation with normalization types
  - `updateNormalizationAmount`: Test automatic recalculation on WBS mapping changes
  - `bulkImportLineItems`: Test CSV parsing and batch creation scenarios

### 7.2 Integration Tests

- Database RPC function tests with sample data
- Form submission and data persistence tests
- Access control and permission tests
- WBS mapping and budget transfer tests

### 7.3 End-to-End Tests

- Complete tender creation and management workflow
- Tender comparison and selection process
- Work package conversion workflow
- Multi-user collaboration scenarios

## 8. Performance Considerations

### 8.1 Database Optimization

- Proper indexing on frequently queried columns
- Efficient RPC functions with minimal database roundtrips
- Pagination for large tender lists
- Caching of frequently accessed data

### 8.2 Frontend Optimization

- Lazy loading of tender comparison data
- Debounced search and filtering
- Optimistic updates for better user experience
- Efficient state management for complex forms

## 9. Security Considerations

### 9.1 Access Control

- Proper RLS policies for all tender-related tables
- Role-based permissions for tender management
- Audit logging for all sensitive operations
- Input validation and sanitization

### 9.2 Data Protection

- Encryption of sensitive tender information
- Secure file upload for tender documents
- Regular security audits and updates
- Compliance with data protection regulations

## 10. Migration Strategy

### 10.1 Data Migration

- No existing data migration required (new feature)
- Import utilities for existing tender data from external systems
- Validation scripts for data integrity

### 10.2 User Training

- User documentation and tutorials
- Training sessions for key stakeholders
- Gradual rollout with pilot projects
- Feedback collection and iteration

## 11. Detailed Component Specifications

### 11.1 Tender List Component (`TenderList.svelte`)

```typescript
import type { Tables } from '$lib/database.types';

// Component props using database types and RPC return types
type TenderListProps = {
	tenders: Database['public']['Functions']['get_project_tenders']['Returns'];
	project: Tables<'project'>;
	canEdit: boolean;
};

// TenderListItem is inferred from the RPC function return type
type TenderListItem = Database['public']['Functions']['get_project_tenders']['Returns'][number];
```

**Features:**

- Sortable table with vendor, tender name, status, amount, coverage (use the documentation at https://shadcn-svelte.com/docs/components/data-table to build the sortable table)
- Status badges with color coding
- Search and filter functionality
- Bulk actions for status updates

### 11.2 Tender Form Component (`TenderForm.svelte`)

```typescript
import { tenderSchema } from '$lib/schemas/tender';
import type { z } from 'zod';

// Form data inferred from Zod schema
type TenderFormData = z.infer<typeof tenderSchema>;
```

**Features:**

- Vendor selection with search/filter capability and ability to "add new"
- Date picker for submission date
- Currency selection dropdown
- Text area for description and notes (not rich text)
- Form validation with real-time feedback
- Auto-save functionality for draft tenders

### 11.3 Line Item Management Component (`LineItemManager.svelte`)

```typescript
import type { Tables } from '$lib/database.types';
import { tenderLineItemSchema, tenderWbsMappingSchema } from '$lib/schemas/tender';
import type { z } from 'zod';

// Line item data from database with WBS mappings
type LineItemData = Tables<'tender_line_item'> & {
	wbs_mappings: (Tables<'tender_wbs_mapping'> & {
		wbs_library_item: Pick<Tables<'wbs_library_item'>, 'code' | 'description'>;
	})[];
};

// WBS mapping inferred from Zod schema
type WbsMapping = z.infer<typeof tenderWbsMappingSchema> & {
	wbs_code: string;
	wbs_description: string;
};
```

**Features:**

- Editable table with inline editing
- Bulk import from CSV/Excel
- Automatic calculation of subtotals
- **Flexible Normalization Input**:
  - Toggle between "Amount" and "Percentage of Budget" input methods
  - Amount mode: Direct currency input for normalization value
  - Percentage mode: Percentage input with real-time calculation against mapped WBS budget amounts
  - Visual indicator showing calculated normalization amount when using percentage mode
  - Validation to ensure normalization value is provided based on selected input type
- WBS mapping interface with search and budget amount lookup for percentage calculations
- **Provisional Sum / Prime Cost Guidance**:
  - Help text encouraging users to note "Provisional Sum" and "Prime Cost" entries in the Unit column
  - Visual hints and/or tooltips explaining how to handle these special tender items
- Validation for required fields and calculations

### 11.4 WBS Mapping Component (`WbsMappingInterface.svelte`)

```typescript
import type { Tables } from '$lib/database.types';

type WbsMappingProps = {
	lineItem: LineItemData;
	availableWbsItems: WbsItem[];
	existingMappings: WbsMapping[];
	onMappingUpdate: (mappings: WbsMapping[]) => void;
};

// WBS item with budget information from active budget version
type WbsItem = Tables<'wbs_library_item'> & {
	budget_amount: number;
	budget_quantity: number;
	budget_unit_rate: number;
};
```

**Features:**

- **Hierarchical WBS Tree Display**: Interactive tree view showing the complete WBS hierarchy with expand/collapse functionality for each level
- **Individual WBS Item Mapping**: Direct mapping to specific leaf-node WBS items with coverage percentage or quantity input
- **Category-Level Mapping Interface**:
  - When mapping to a category (parent WBS item), display all child items with individual checkboxes
  - Allow selective inclusion/exclusion of specific child WBS codes within the category
  - Provide "Select All" and "Select None" options for category children
  - Show visual hierarchy indentation to clarify parent-child relationships
- **Cross-Hierarchy Mapping Support**:
  - Advanced interface for mapping line items that span multiple WBS sections
  - Search functionality to find and select WBS items from different parts of the hierarchy
  - Simple interface for adding WBS items from different categories
  - Visual grouping of selected WBS items by their hierarchy location
- **Coverage Input Methods**:
  - Percentage-based coverage (0-100%) with slider and numeric input
  - Quantity-based coverage with unit validation against budget quantities
  - Automatic calculation switching between percentage and quantity modes
  - Warning indicators when coverage exceeds 100% across all mappings for a WBS item
- **Mapping Validation & Feedback**:
  - Real-time validation of total coverage percentages across all tenders
  - Visual indicators for complete (100%), partial (<100%), and over-allocated (>100%) WBS items
  - Conflict detection when multiple line items map to the same WBS item
  - Suggestions for budget transfers when mappings create allocation conflicts
- **Budget Transfer Integration**:
  - Automatic detection of scenarios requiring budget transfers
  - One-click budget transfer suggestions with amount calculations
  - Audit trail display for all executed transfers
- **Mapping Management Tools**:
  - Bulk mapping operations for similar line items
  - Copy mapping patterns from other line items or tenders
  - Template-based mapping for common tender structures
  - Export/import mapping configurations for reuse

### 11.5 Tender Comparison Component (`TenderComparison.svelte`)

```typescript
// Comparison data inferred from RPC function return type
type ComparisonData =
	Database['public']['Functions']['get_tender_comparison_data']['Returns'][number];

// Tender line data is part of the JSONB tender_data field in ComparisonData
type TenderLineData = {
	tender_id: string;
	vendor_name: string;
	tender_name: string;
	line_item_id: string;
	line_description: string;
	quantity?: number;
	unit?: string;
	unit_rate?: number;
	subtotal?: number;
	coverage_percentage: number;
	coverage_quantity?: number;
	normalization_type: 'amount' | 'percentage';
	normalization_amount?: number;
	normalization_percentage?: number;
};
```

**Features:**

- **Hierarchical WBS Organization**:
  - Table structure organized by WBS hierarchy with expandable/collapsible sections
  - Visual indentation and tree controls to show parent-child relationships
  - Breadcrumb navigation for deep WBS structures
  - Quick jump to specific WBS levels (Level 1, 2, 3, etc.)
- **Multi-Tender Side-by-Side Comparison**:
  - Dynamic columns for each tender with vendor name and tender identification
  - Tender status indicators in column headers (submitted, under_review, selected, rejected)
  - Synchronized scrolling across tender columns
  - Sticky header with tender names, status, and key metrics
  - Responsive column width adjustment based on content
- **Advanced Filtering & Selection**:
  - Multi-select WBS category filtering with hierarchical checkboxes
  - Filter by multiple WBS sections simultaneously with AND/OR logic
  - Tender-specific filtering (show/hide individual tenders)
  - Status-based filtering (submitted, under review, selected, rejected)
  - Search functionality across WBS codes and descriptions
  - URL parameter persistence for shareable filtered views
- **Price Variance Analysis**:
  - Color-coded variance indicators (red for over-budget, green for under-budget)
  - Percentage variance calculations from original budget amounts
  - Absolute variance amounts with currency formatting
  - Variance threshold settings for highlighting significant differences
  - Trend indicators showing variance patterns across WBS items
- **Coverage & Scope Validation**:
  - Visual coverage percentage indicators showing percentage of budget amount covered by tenders
  - Budget coverage calculations based on tender amounts vs. original budget amounts
  - Warning badges for partial budget coverage (<100%) requiring normalization
  - Error indicators for over-allocation (>100% total budget coverage)
  - Coverage completeness summary showing percentage of total project budget covered
  - Budget coverage metrics at category and project levels
  - Normalization amount tracking and validation
- **Aggregated Totals & Summaries**:
  - Real-time calculation of totals for filtered WBS sections
  - Tender-wise grand totals with currency conversion if needed
  - Budget vs. tender variance summaries
  - Budget coverage statistics (% of total budget amount covered by tenders)
  - Coverage completeness metrics (% of WBS items with complete budget coverage)
  - Cost per unit comparisons where applicable
- **Interactive Data Management**:
  - Inline editing of normalization amounts
  - Quick actions for budget transfers between WBS items
  - Bulk operations for similar line items across tenders
  - Comments and notes attachment to specific comparisons
- **Collaboration Features**:
  - Real-time updates when other users modify tender data
  - Comment threads on specific WBS items or tender lines
  - Approval workflow integration for tender selection decisions
  - Audit trail of all comparison view interactions and decisions

### 11.6 Scoring Interface Component (`TenderScoring.svelte`)

```typescript
import type { Tables } from '$lib/database.types';

type ScoringData = {
	tender: Tables<'tender'>;
	criteria: Tables<'tender_scoring_criteria'>[];
	existingScores: Tables<'tender_score'>[];
	canEdit: boolean;
};

// These types are directly from database tables
type ScoringCriteria = Tables<'tender_scoring_criteria'>;
type TenderScore = Tables<'tender_score'>;
```

**Features:**

- Slider inputs for 0-10 scoring scale
- Weighted average calculation input and display
- Comments section for each criterion
- Score history and revision tracking
- Bulk scoring for multiple tenders
- Score comparison across tenders

## 12. Advanced RPC Functions

### 12.1 Tender Analytics Function

```sql
CREATE OR REPLACE FUNCTION "public"."get_tender_analytics"(
    "project_id_param" uuid
) RETURNS TABLE (
    total_tenders integer,
    submitted_count integer,
    under_review_count integer,
    selected_count integer,
    rejected_count integer,
    average_tender_amount numeric(20, 4),
    total_tender_value numeric(20, 4),
    wbs_coverage_stats jsonb,
    vendor_participation jsonb,
    scoring_summary jsonb
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    WITH tender_stats AS (
        SELECT
            COUNT(*)::integer as total_tenders,
            COUNT(*) FILTER (WHERE status = 'submitted')::integer as submitted_count,
            COUNT(*) FILTER (WHERE status = 'under_review')::integer as under_review_count,
            COUNT(*) FILTER (WHERE status = 'selected')::integer as selected_count,
            COUNT(*) FILTER (WHERE status = 'rejected')::integer as rejected_count,
            AVG(
                (SELECT SUM(tli.subtotal)
                 FROM public.tender_line_item tli
                 JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
                 WHERE tr.tender_id = t.tender_id AND tr.is_current = true)
            ) as average_tender_amount,
            SUM(
                (SELECT SUM(tli.subtotal)
                 FROM public.tender_line_item tli
                 JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
                 WHERE tr.tender_id = t.tender_id AND tr.is_current = true)
            ) as total_tender_value
        FROM public.tender t
        WHERE t.project_id = project_id_param
    ),
    wbs_coverage AS (
        SELECT jsonb_build_object(
            'total_wbs_items', COUNT(DISTINCT wli.wbs_library_item_id),
            'mapped_wbs_items', COUNT(DISTINCT twm.wbs_library_item_id),
            'coverage_percentage',
            CASE
                WHEN COUNT(DISTINCT wli.wbs_library_item_id) > 0 THEN
                    (COUNT(DISTINCT twm.wbs_library_item_id)::numeric / COUNT(DISTINCT wli.wbs_library_item_id)::numeric) * 100
                ELSE 0
            END
        ) as wbs_coverage_stats
        FROM public.wbs_library_item wli
        JOIN public.budget_version_item bvi ON wli.wbs_library_item_id = bvi.wbs_library_item_id
        JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
        JOIN public.project p ON bv.project_id = p.project_id
        LEFT JOIN public.tender_wbs_mapping twm ON wli.wbs_library_item_id = twm.wbs_library_item_id
        LEFT JOIN public.tender_line_item tli ON twm.tender_line_item_id = tli.tender_line_item_id
        LEFT JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id AND tr.is_current = true
        LEFT JOIN public.tender t ON tr.tender_id = t.tender_id AND t.project_id = project_id_param
        WHERE p.project_id = project_id_param AND p.active_budget_version_id = bv.budget_version_id
    ),
    vendor_stats AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'vendor_id', v.vendor_id,
                'vendor_name', v.name,
                'tender_count', COUNT(t.tender_id),
                'total_value', SUM(
                    (SELECT SUM(tli.subtotal)
                     FROM public.tender_line_item tli
                     JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
                     WHERE tr.tender_id = t.tender_id AND tr.is_current = true)
                ),
                'average_score', AVG(ts.score)
            )
        ) as vendor_participation
        FROM public.vendor v
        JOIN public.tender t ON v.vendor_id = t.vendor_id
        LEFT JOIN public.tender_score ts ON t.tender_id = ts.tender_id
        WHERE t.project_id = project_id_param
        GROUP BY v.vendor_id, v.name
    ),
    scoring_stats AS (
        SELECT jsonb_build_object(
            'scored_tenders', COUNT(DISTINCT ts.tender_id),
            'total_tenders', (SELECT COUNT(*) FROM public.tender WHERE project_id = project_id_param),
            'average_overall_score', AVG(ts.score),
            'score_distribution', jsonb_build_object(
                '0-2', COUNT(*) FILTER (WHERE ts.score >= 0 AND ts.score < 2),
                '2-4', COUNT(*) FILTER (WHERE ts.score >= 2 AND ts.score < 4),
                '4-6', COUNT(*) FILTER (WHERE ts.score >= 4 AND ts.score < 6),
                '6-8', COUNT(*) FILTER (WHERE ts.score >= 6 AND ts.score < 8),
                '8-10', COUNT(*) FILTER (WHERE ts.score >= 8 AND ts.score <= 10)
            )
        ) as scoring_summary
        FROM public.tender_score ts
        JOIN public.tender t ON ts.tender_id = t.tender_id
        WHERE t.project_id = project_id_param
    )
    SELECT
        ts.total_tenders,
        ts.submitted_count,
        ts.under_review_count,
        ts.selected_count,
        ts.rejected_count,
        ts.average_tender_amount,
        ts.total_tender_value,
        wc.wbs_coverage_stats,
        vs.vendor_participation,
        ss.scoring_summary
    FROM tender_stats ts
    CROSS JOIN wbs_coverage wc
    CROSS JOIN vendor_stats vs
    CROSS JOIN scoring_stats ss;
END;
$$;
```

### 12.2 Budget Transfer Validation Function

```sql
CREATE OR REPLACE FUNCTION "public"."validate_budget_transfer"(
    "project_id_param" uuid,
    "from_wbs_item_id" uuid,
    "to_wbs_item_id" uuid,
    "transfer_amount" numeric(20, 4)
) RETURNS TABLE (
    is_valid boolean,
    error_message text,
    from_available_amount numeric(20, 4),
    to_current_amount numeric(20, 4),
    existing_transfers numeric(20, 4)
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
DECLARE
    from_budget_amount numeric(20, 4);
    from_used_amount numeric(20, 4);
    from_transferred_out numeric(20, 4);
    from_transferred_in numeric(20, 4);
    available_amount numeric(20, 4);
    to_budget_amount numeric(20, 4);
    to_transferred_in numeric(20, 4);
    to_transferred_out numeric(20, 4);
    current_to_amount numeric(20, 4);
    existing_transfer_amount numeric(20, 4);
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Get original budget amount for from_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO from_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = from_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Get original budget amount for to_wbs_item
    SELECT bvi.quantity * bvi.unit_rate * COALESCE(bvi.factor, 1)
    INTO to_budget_amount
    FROM public.budget_version_item bvi
    JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
    JOIN public.project p ON bv.project_id = p.project_id
    WHERE bvi.wbs_library_item_id = to_wbs_item_id
    AND p.project_id = project_id_param
    AND p.active_budget_version_id = bv.budget_version_id;

    -- Calculate existing transfers
    SELECT
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = from_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO from_transferred_out, from_transferred_in
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    SELECT
        COALESCE(SUM(CASE WHEN to_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN from_wbs_library_item_id = to_wbs_item_id THEN transfer_amount ELSE 0 END), 0)
    INTO to_transferred_in, to_transferred_out
    FROM public.budget_transfer
    WHERE project_id = project_id_param;

    -- Check for existing transfer between these specific items
    SELECT COALESCE(SUM(transfer_amount), 0)
    INTO existing_transfer_amount
    FROM public.budget_transfer
    WHERE project_id = project_id_param
    AND from_wbs_library_item_id = from_wbs_item_id
    AND to_wbs_library_item_id = to_wbs_item_id;

    -- Calculate available amount (original budget + transfers in - transfers out)
    available_amount := COALESCE(from_budget_amount, 0) + COALESCE(from_transferred_in, 0) - COALESCE(from_transferred_out, 0);
    current_to_amount := COALESCE(to_budget_amount, 0) + COALESCE(to_transferred_in, 0) - COALESCE(to_transferred_out, 0);

    -- Validate transfer
    RETURN QUERY
    SELECT
        CASE
            WHEN from_budget_amount IS NULL THEN false
            WHEN to_budget_amount IS NULL THEN false
            WHEN transfer_amount <= 0 THEN false
            WHEN transfer_amount > available_amount THEN false
            ELSE true
        END as is_valid,
        CASE
            WHEN from_budget_amount IS NULL THEN 'Source WBS item not found in budget'
            WHEN to_budget_amount IS NULL THEN 'Target WBS item not found in budget'
            WHEN transfer_amount <= 0 THEN 'Transfer amount must be positive'
            WHEN transfer_amount > available_amount THEN 'Transfer amount exceeds available budget'
            ELSE NULL
        END as error_message,
        available_amount as from_available_amount,
        current_to_amount as to_current_amount,
        existing_transfer_amount as existing_transfers;
END;
$$;
```

### 12.3 Create Budget Transfer Function

```sql
CREATE OR REPLACE FUNCTION "public"."create_budget_transfer"(
    "project_id_param" uuid,
    "line_item_id_param" uuid,
    "from_wbs_item_id" uuid,
    "to_wbs_item_id" uuid,
    "transfer_amount" numeric(20, 4),
    "reason" text
) RETURNS TABLE (
    budget_transfer_id uuid,
    is_valid boolean,
    error_message text
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
DECLARE
    validation_result record;
    new_transfer_id uuid;
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_role('project', project_id_param, 'editor') THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    -- Validate the transfer first
    SELECT * INTO validation_result
    FROM public.validate_budget_transfer(
        project_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount
    );

    -- If validation fails, return error
    IF NOT validation_result.is_valid THEN
        RETURN QUERY
        SELECT
            NULL::uuid as budget_transfer_id,
            false as is_valid,
            validation_result.error_message;
        RETURN;
    END IF;

    -- Create the budget transfer
    INSERT INTO public.budget_transfer (
        project_id,
        tender_line_item_id,
        from_wbs_library_item_id,
        to_wbs_library_item_id,
        transfer_amount,
        reason,
        created_by_user_id
    ) VALUES (
        project_id_param,
        line_item_id_param,
        from_wbs_item_id,
        to_wbs_item_id,
        transfer_amount,
        reason,
        auth.uid()
    ) RETURNING budget_transfer_id INTO new_transfer_id;

    -- Return success
    RETURN QUERY
    SELECT
        new_transfer_id,
        true as is_valid,
        NULL::text as error_message;
END;
$$;
```

### 12.4 List Budget Transfers Function

```sql
CREATE OR REPLACE FUNCTION "public"."get_project_budget_transfers"(
    "project_id_param" uuid
) RETURNS TABLE (
    budget_transfer_id uuid,
    tender_line_item_id uuid,
    from_wbs_code text,
    from_wbs_description text,
    to_wbs_code text,
    to_wbs_description text,
    transfer_amount numeric(20, 4),
    reason text,
    created_at timestamptz,
    created_by_name text,
    tender_name text,
    vendor_name text
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = '' AS $$
BEGIN
    -- Verify user has access to project
    IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
        RAISE EXCEPTION 'Access denied to project';
    END IF;

    RETURN QUERY
    SELECT
        bt.budget_transfer_id,
        bt.tender_line_item_id,
        from_wbs.code as from_wbs_code,
        from_wbs.description as from_wbs_description,
        to_wbs.code as to_wbs_code,
        to_wbs.description as to_wbs_description,
        bt.transfer_amount,
        bt.reason,
        bt.created_at,
        p.full_name as created_by_name,
        t.tender_name,
        v.name as vendor_name
    FROM public.budget_transfer bt
    JOIN public.wbs_library_item from_wbs ON bt.from_wbs_library_item_id = from_wbs.wbs_library_item_id
    JOIN public.wbs_library_item to_wbs ON bt.to_wbs_library_item_id = to_wbs.wbs_library_item_id
    JOIN public.profile p ON bt.created_by_user_id = p.user_id
    LEFT JOIN public.tender_line_item tli ON bt.tender_line_item_id = tli.tender_line_item_id
    LEFT JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
    LEFT JOIN public.tender t ON tr.tender_id = t.tender_id
    LEFT JOIN public.vendor v ON t.vendor_id = v.vendor_id
    WHERE bt.project_id = project_id_param
    ORDER BY bt.created_at DESC;
END;
$$;
```

## 13. Form Action Implementations

### 13.1 Tender Creation Action

```typescript
// src/lib/components/forms/tender/tender_form_actions.ts
import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate, message } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { tenderSchema } from '$lib/schemas/tender';
import { redirect } from 'sveltekit-flash-message/server';
import { requireProject, requireUser } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const createTender = async ({ request, locals, cookies }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { org_name, client_name, project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	const form = await superValidate(request, zod(tenderSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Start a transaction for tender and initial revision creation
	const { data: tender, error: tenderError } = await supabase
		.from('tender')
		.insert({
			...form.data,
			project_id: project_id,
			created_by_user_id: user.id,
		})
		.select('tender_id, tender_name')
		.single();

	if (tenderError) {
		locals.log.error({ msg: `Error creating tender: ${JSON.stringify(tenderError)}` });
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create tender' },
		});
	}

	// Create initial revision
	const { error: revisionError } = await supabase.from('tender_revision').insert({
		tender_id: tender.tender_id,
		revision_number: 1,
		revision_date: new Date().toISOString().split('T')[0],
		revision_notes: 'Initial tender submission',
		is_current: true,
		created_by_user_id: user.id,
	});

	if (revisionError) {
		locals.log.error({ msg: `Error creating tender revision: ${JSON.stringify(revisionError)}` });
		// Clean up the tender if revision creation fails
		await supabase.from('tender').delete().eq('tender_id', tender.tender_id);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create tender revision' },
		});
	}

	throw redirect(
		`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_id_short)}/tenders/${tender.tender_id}`,
		{
			type: 'success',
			message: `Tender "${tender.tender_name}" created successfully`,
		},
		cookies,
	);
};

// First, define a schema for tender status updates
const tenderStatusSchema = z.object({
	tender_id: z.uuid('Invalid tender ID'),
	status: z.enum(['submitted', 'under_review', 'selected', 'rejected']),
});

export const updateTenderStatus = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(tenderStatusSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	const { tender_id, status } = form.data;

	const { error } = await supabase
		.from('tender')
		.update({ status, updated_at: new Date().toISOString() })
		.eq('tender_id', tender_id);

	if (error) {
		locals.log.error({ msg: `Error updating tender status: ${JSON.stringify(error)}` });
		return fail(500, { message: { type: 'error', text: 'Failed to update tender status' } });
	}

	return { message: { type: 'success', text: 'Tender status updated successfully' } };
};
```

### 13.2 Line Item Management Action

```typescript
// src/lib/components/forms/tender/line_item_actions.ts
import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { tenderLineItemSchema } from '$lib/schemas/tender';
import { requireUser } from '$lib/server/auth';

// Define schema for line item creation including revision ID
const createLineItemSchema = tenderLineItemSchema.extend({
	tender_revision_id: z.uuid('Tender revision ID is required'),
});

export const createLineItem = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(createLineItemSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	const { tender_revision_id, ...lineItemData } = form.data;

	// Check if line number already exists
	const { data: existingLineItem } = await supabase
		.from('tender_line_item')
		.select('tender_line_item_id')
		.eq('tender_revision_id', tender_revision_id)
		.eq('line_number', lineItemData.line_number)
		.single();

	if (existingLineItem) {
		return fail(400, {
			form,
			message: { type: 'error', text: 'Line number already exists' },
		});
	}

	// Calculate normalization amount if percentage type is used
	let finalLineItemData = { ...lineItemData };
	if (lineItemData.normalization_type === 'percentage' && lineItemData.normalization_percentage) {
		// Note: This calculation would need to happen after WBS mappings are created
		// For now, we'll store the percentage and calculate the amount later
		finalLineItemData.normalization_amount = null;
	}

	const { data: lineItem, error } = await supabase
		.from('tender_line_item')
		.insert({
			...finalLineItemData,
			tender_revision_id,
		})
		.select('tender_line_item_id')
		.single();

	if (error) {
		locals.log.error({ msg: `Error creating line item: ${JSON.stringify(error)}` });
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create line item' },
		});
	}

	return {
		form,
		lineItem,
		message: { type: 'success', text: 'Line item created successfully' },
	};
};

// Define schema for bulk import
const bulkImportSchema = z.object({
	tender_revision_id: z.uuid('Tender revision ID is required'),
	csv_data: z.string().min(1, 'CSV data is required'),
});

export const bulkImportLineItems = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(bulkImportSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	const { tender_revision_id, csv_data } = form.data;

	try {
		// Parse CSV data (simplified - in production, use a proper CSV parser)
		const lines = csv_data.split('\n').filter((line) => line.trim());
		const headers = lines[0].split(',').map((h) => h.trim());
		const dataLines = lines.slice(1);

		const lineItems = dataLines.map((line, index) => {
			const values = line.split(',').map((v) => v.trim());
			const lineItem: any = {
				tender_revision_id,
				line_number: index + 1,
			};

			headers.forEach((header, i) => {
				const value = values[i];
				switch (header.toLowerCase()) {
					case 'description':
						lineItem.description = value;
						break;
					case 'quantity':
						lineItem.quantity = value ? parseFloat(value) : null;
						break;
					case 'unit':
						lineItem.unit = value || null;
						break;
					case 'unit_rate':
						lineItem.unit_rate = value ? parseFloat(value) : null;
						break;
					case 'subtotal':
						lineItem.subtotal = value ? parseFloat(value) : null;
						break;
					// Add more field mappings as needed
				}
			});

			return lineItem;
		});

		// Validate all line items
		const validationErrors: string[] = [];
		for (const [index, item] of lineItems.entries()) {
			if (!item.description) {
				validationErrors.push(`Line ${index + 1}: Description is required`);
			}
		}

		if (validationErrors.length > 0) {
			return fail(400, {
				message: { type: 'error', text: validationErrors.join('; ') },
			});
		}

		// Insert all line items
		const { data, error } = await supabase
			.from('tender_line_item')
			.insert(lineItems)
			.select('tender_line_item_id');

		if (error) {
			locals.log.error({ msg: `Error bulk importing line items: ${JSON.stringify(error)}` });
			return fail(500, {
				message: { type: 'error', text: 'Failed to import line items' },
			});
		}

		return {
			message: {
				type: 'success',
				text: `Successfully imported ${data?.length || 0} line items`,
			},
		};
	} catch (parseError) {
		locals.log.error({ msg: `Error parsing CSV data: ${parseError}` });
		return fail(400, {
			message: { type: 'error', text: 'Invalid CSV format' },
		});
	}
};

// Update normalization amount when WBS mappings change
export const updateNormalizationAmount = async ({ request, locals }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const updateNormalizationSchema = z.object({
		tender_line_item_id: z.uuid('Line item ID is required'),
	});

	const form = await superValidate(request, zod(updateNormalizationSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	const { tender_line_item_id } = form.data;

	// Get the line item to check if it uses percentage normalization
	const { data: lineItem, error: lineItemError } = await supabase
		.from('tender_line_item')
		.select('normalization_type, normalization_percentage')
		.eq('tender_line_item_id', tender_line_item_id)
		.single();

	if (lineItemError || !lineItem) {
		return fail(404, { message: { type: 'error', text: 'Line item not found' } });
	}

	// Only update if using percentage normalization
	if (lineItem.normalization_type === 'percentage' && lineItem.normalization_percentage) {
		const { data: calculatedAmount, error: calcError } = await supabase.rpc(
			'calculate_normalization_amount',
			{
				line_item_id_param: tender_line_item_id,
				normalization_percentage_param: lineItem.normalization_percentage,
			},
		);

		if (calcError) {
			locals.log.error({ msg: 'Error calculating normalization amount:', calcError });
			return fail(500, {
				message: { type: 'error', text: 'Failed to calculate normalization amount' },
			});
		}

		// Update the normalization amount
		const { error: updateError } = await supabase
			.from('tender_line_item')
			.update({ normalization_amount: calculatedAmount })
			.eq('tender_line_item_id', tender_line_item_id);

		if (updateError) {
			locals.log.error({ msg: 'Error updating normalization amount:', updateError });
			return fail(500, {
				message: { type: 'error', text: 'Failed to update normalization amount' },
			});
		}
	}

	return { success: true, message: { type: 'success', text: 'Normalization amount updated' } };
};
```

## 14. Load Function Examples

### 14.1 Tender List Load Function

```typescript
// src/routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/tenders/+page.server.ts
import type { PageServerLoad } from './$types';
import { requireProject, requireUser } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { tenderSchema } from '$lib/schemas/tender';

export const load: PageServerLoad = async ({ locals, url }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get search and filter parameters
	const search = url.searchParams.get('search') || '';
	const status = url.searchParams.get('status') || '';
	const vendor = url.searchParams.get('vendor') || '';

	// Fetch tenders using RPC function
	const { data: tenders, error: tendersError } = await supabase.rpc('get_project_tenders', {
		project_id_param: project_id,
	});

	if (tendersError) {
		locals.log.error({ msg: 'Error fetching tenders:', tendersError });
		throw new Error('Failed to fetch tenders');
	}

	// Fetch accessible vendors for filter dropdown
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: (await requireUser()).user.id,
		entity_type_param: 'project',
		entity_id_param: project_id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors:', vendorsError });
	}

	// Initialize form for new tender creation
	const form = await superValidate(zod(tenderSchema));

	return {
		tenders: tenders || [],
		vendors: vendors || [],
		form,
		filters: {
			search,
			status,
			vendor,
		},
	};
};
```

### 14.2 Tender Comparison Load Function

```typescript
// src/routes/org/[org_name]/clients/[client_name]/projects/[project_id_short]/tenders/comparison/+page.server.ts
import type { PageServerLoad } from './$types';
import { requireProject, requireUser } from '$lib/server/auth';
import { projectUUID } from '$lib/schemas/project';

export const load: PageServerLoad = async ({ locals, url }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Get filter parameters from URL
	const selectedTenders = url.searchParams.getAll('tender');
	const wbsFilter = url.searchParams.getAll('wbs');
	const hideZeroValues = url.searchParams.get('hideZero') === 'true';

	// Fetch comparison data using RPC function
	const { data: comparisonData, error: comparisonError } = await supabase.rpc(
		'get_tender_comparison_data',
		{ project_id_param: project_id },
	);

	if (comparisonError) {
		locals.log.error({ msg: 'Error fetching comparison data:', comparisonError });
		throw new Error('Failed to fetch comparison data');
	}

	// Fetch tender list for filter controls
	const { data: tenders, error: tendersError } = await supabase.rpc('get_project_tenders', {
		project_id_param: project_id,
	});

	if (tendersError) {
		locals.log.error({ msg: 'Error fetching tenders:', tendersError });
	}

	// Fetch WBS hierarchy for filtering
	const { data: wbsItems, error: wbsError } = await supabase
		.from('wbs_library_item')
		.select('wbs_library_item_id, code, description, level, parent_item_id')
		.eq('project_id', project_id)
		.order('code');

	if (wbsError) {
		locals.log.error({ msg: 'Error fetching WBS items:', wbsError });
	}

	return {
		comparisonData: comparisonData || [],
		tenders: tenders || [],
		wbsItems: wbsItems || [],
		filters: {
			selectedTenders,
			wbsFilter,
			hideZeroValues,
		},
	};
};
```

## 15. User Acceptance Criteria

### 15.1 Tender Management

- [ ] **AC-TM-001**: Users can create new tenders with vendor selection and basic information
- [ ] **AC-TM-002**: Users can edit tender details including name, description, and submission date
- [ ] **AC-TM-003**: Users can update tender status (submitted, under review, selected, rejected)
- [ ] **AC-TM-004**: Users can view tender list with sorting and filtering capabilities
- [ ] **AC-TM-005**: Users can delete tenders (owner permission required)
- [ ] **AC-TM-006**: System tracks tender creation and modification history
- [ ] **AC-TM-007**: Users can select currency from predefined currency table with proper symbols and formatting

### 15.2 Tender Revisions

- [ ] **AC-TR-001**: Users can create new revisions of existing tenders
- [ ] **AC-TR-002**: System maintains revision history with version numbers
- [ ] **AC-TR-003**: Only one revision can be marked as current at a time
- [ ] **AC-TR-004**: Users can view and compare different revisions
- [ ] **AC-TR-005**: Line items are associated with specific revisions
- [ ] **AC-TR-006**: System automatically sets previous revisions to non-current when creating new current revision
- [ ] **AC-TR-007**: Database enforces only one current revision per tender through partial unique index

### 15.3 Line Item Management

- [ ] **AC-LI-001**: Users can add line items with description, quantity, rates, and subtotal
- [ ] **AC-LI-002**: Users can edit existing line items within current revision
- [ ] **AC-LI-003**: Users can reorder line items using drag-and-drop
- [ ] **AC-LI-004**: Users can bulk import line items from CSV/Excel files
- [ ] **AC-LI-005**: System validates required fields and calculations
- [ ] **AC-LI-006**: Users can add notes to individual line items
- [ ] **AC-LI-007**: System provides guidance for handling Provisional Sum and Prime Cost entries
- [ ] **AC-LI-008**: Users are encouraged to note special items in the Unit column with helpful UI hints
- [ ] **AC-LI-009**: Users can choose between amount and percentage normalization input methods
- [ ] **AC-LI-010**: System calculates normalization amounts from percentages using mapped WBS budget amounts
- [ ] **AC-LI-011**: System validates normalization input based on selected type (amount or percentage)
- [ ] **AC-LI-012**: System automatically recalculates normalization amounts when WBS mappings change

### 15.4 WBS Mapping

- [ ] **AC-WM-001**: Users can map line items to one or more WBS codes
- [ ] **AC-WM-002**: Users can specify coverage percentage for each WBS mapping
- [ ] **AC-WM-003**: Users can specify coverage quantity as alternative to percentage
- [ ] **AC-WM-004**: System validates that coverage percentages don't exceed 100%
- [ ] **AC-WM-005**: Users can map category-level items with child WBS selection
- [ ] **AC-WM-006**: System provides visual indicators for mapping completeness

### 15.5 Budget Transfers

- [ ] **AC-BT-001**: Users can transfer budget amounts between WBS items
- [ ] **AC-BT-002**: System validates transfer amounts against available budget
- [ ] **AC-BT-003**: System tracks all budget transfers for audit purposes
- [ ] **AC-BT-004**: Users can view transfer history and impact on WBS items
- [ ] **AC-BT-005**: System prevents transfers that would create negative balances
- [ ] **AC-BT-006**: System provides RPC function to create budget transfers with validation
- [ ] **AC-BT-007**: System provides RPC function to list all budget transfers for a project
- [ ] **AC-BT-008**: Budget transfer creation includes tender line item association and reason tracking

### 15.6 Tender Scoring

- [ ] **AC-TS-001**: Users can define scoring criteria with weights and max scores
- [ ] **AC-TS-002**: Users can score tenders on 0-10 scale for each criterion
- [ ] **AC-TS-003**: System calculates weighted average scores automatically
- [ ] **AC-TS-004**: Users can add comments to individual scores
- [ ] **AC-TS-005**: Users can revise scores with history tracking
- [ ] **AC-TS-006**: System displays score comparisons across tenders

### 15.7 Tender Comparison

- [ ] **AC-TC-001**: Users can view side-by-side comparison of tenders by WBS item
- [ ] **AC-TC-002**: System highlights price variances from original budget
- [ ] **AC-TC-003**: Users can filter comparison by WBS sections with URL persistence
- [ ] **AC-TC-004**: Users can show/hide individual tenders in comparison
- [ ] **AC-TC-005**: System validates normalization for partial coverage items
- [ ] **AC-TC-006**: Users can export comparison data to Excel/PDF
- [ ] **AC-TC-007**: System compares coverage by percentage of budget amount covered, not quantity
- [ ] **AC-TC-008**: System displays budget coverage statistics showing percentage of total budget covered
- [ ] **AC-TC-009**: System shows tender status in column headers, not at line item level

### 15.8 Work Package Conversion

- [ ] **AC-WP-001**: Users can select tenders for conversion to work packages
- [ ] **AC-WP-002**: System creates work packages containing multiple WBS codes
- [ ] **AC-WP-003**: System maintains relationship between tenders and work packages
- [ ] **AC-WP-004**: Users can create multiple purchase orders per work package
- [ ] **AC-WP-005**: System prevents duplicate conversions of same tender
- [ ] **AC-WP-006**: Conversion process includes audit trail and notes
- [ ] **AC-WP-007**: Work packages are created using tender_work_package table, not legacy work_package table
- [ ] **AC-WP-008**: Work package creation includes name and description fields for proper identification

### 15.9 Access Control & Security

- [ ] **AC-AC-001**: Only users with project access can view tenders
- [ ] **AC-AC-002**: Only users with editor role can create/modify tenders
- [ ] **AC-AC-003**: Only users with owner role can delete tenders
- [ ] **AC-AC-004**: All tender operations are logged for audit purposes
- [ ] **AC-AC-005**: RLS policies prevent unauthorized data access
- [ ] **AC-AC-006**: Form validation prevents malicious input

### 15.10 Performance & Usability

- [ ] **AC-PU-001**: Tender list loads within 2 seconds for projects with 100+ tenders
- [ ] **AC-PU-002**: Comparison view loads within 3 seconds for complex WBS structures
- [ ] **AC-PU-003**: Forms provide real-time validation feedback
- [ ] **AC-PU-004**: System works on desktop and tablet devices
- [ ] **AC-PU-005**: All user actions provide appropriate feedback messages
- [ ] **AC-PU-006**: System gracefully handles network errors and timeouts

## 16. Error Handling & Edge Cases

### 16.1 Data Validation Errors

- Invalid tender data (missing required fields, invalid formats)
- Line item calculation mismatches
- WBS mapping conflicts (overlapping coverage)
- Budget transfer validation failures
- Scoring outside valid ranges

### 16.2 Business Logic Errors

- Attempting to modify non-current revisions
- Creating duplicate line numbers within revision
- Mapping to non-existent WBS items
- Converting already-converted tenders
- Deleting tenders with associated work packages

### 16.3 System Errors

- Database connection failures
- RPC function execution errors
- File upload/import failures
- Concurrent modification conflicts
- Permission/access control violations

### 16.4 User Experience Errors

- Network timeouts during form submission
- Browser compatibility issues
- Large dataset rendering performance
- Mobile device limitations
- Accessibility compliance failures

## 17. Monitoring & Maintenance

### 17.1 Performance Monitoring

- Database query execution times
- Page load performance metrics
- API response times
- User interaction analytics
- Error rate tracking

### 17.2 Data Quality Monitoring

- Tender data completeness checks
- WBS mapping coverage validation
- Budget transfer consistency audits
- Scoring data integrity checks
- Work package conversion accuracy

### 17.3 Maintenance Procedures

- Regular database optimization
- Index maintenance and updates
- Audit log cleanup procedures
- Performance tuning guidelines
- Security update protocols

This comprehensive implementation plan provides detailed specifications for building a robust tender analysis workflow system that integrates seamlessly with the existing project controls application. The plan includes database design, API specifications, UI components, testing strategies, and user acceptance criteria to ensure successful delivery and adoption.

## Implementation Progress Summary

### Phase 1: Database Foundation ✅ COMPLETED

- All database schemas, tables, and relationships implemented
- Row Level Security (RLS) policies configured
- Audit tables and triggers set up
- Core RPC functions for tender operations created
- Database types generated and integrated

### Phase 2: Core API & Schemas ✅ COMPLETED

**Files Created/Modified:**

- `src/lib/schemas/tender.ts` - Comprehensive Zod validation schemas for all tender entities
- `src/lib/tender_utils.ts` - Complete utility functions for tender CRUD operations
- `src/lib/tender_form_actions.ts` - Form actions for normalization updates
- `src/tests/unit/tender/tender-schema.spec.ts` - Unit tests for schema validation

**Key Features Implemented:**

- Short-uuid utilities for tender, line item, and revision IDs
- TypeScript type definitions using database types and extended relationship types
- Comprehensive Zod schemas: tenderSchema, tenderLineItemSchema, tenderWbsMappingSchema, tenderScoringCriteriaSchema, etc.
- CRUD operations: createTender, updateTender, deleteTender, getTenderById, createTenderRevision, createLineItem, updateLineItem, etc.
- Normalization calculation support (both amount-based and percentage-based)
- Form actions for updating normalization amounts
- Complete unit test coverage with 15 passing test cases

**Technical Achievements:**

- All TypeScript compilation errors resolved
- Database field mappings corrected (e.g., currency vs currency_code)
- Proper error handling with SvelteKit's error function
- Database operations with Supabase client integration
- Schema validation working correctly for all tender entities

### Next Steps: Phase 3 - Basic UI Components

Ready to proceed with implementing the user interface components for tender management, including tender list pages, creation forms, detail views, and line item management interfaces.
