import { describe, it, expect } from 'vitest';
import {
	validateCoveragePercentages,
	getWbsItemStatus,
	getStatusColor,
	getStatusLabel,
} from './wbs_mapping_validation';

describe('WBS Mapping Validation', () => {
	describe('validateCoveragePercentages', () => {
		it('should validate coverage percentages within 100%', () => {
			const mappings = [
				{ coverage_percentage: 50 },
				{ coverage_percentage: 30 },
				{ coverage_percentage: 20 },
			];

			const result = validateCoveragePercentages(mappings);

			expect(result.is_valid).toBe(true);
			expect(result.total_percentage).toBe(100);
			expect(result.error_message).toBeUndefined();
		});

		it('should invalidate coverage percentages exceeding 100%', () => {
			const mappings = [{ coverage_percentage: 60 }, { coverage_percentage: 50 }];

			const result = validateCoveragePercentages(mappings);

			expect(result.is_valid).toBe(false);
			expect(result.total_percentage).toBe(110);
			expect(result.error_message).toBe('Total coverage (110%) exceeds 100%');
		});

		it('should handle mappings without coverage percentages', () => {
			const mappings = [{ coverage_percentage: undefined }, { coverage_percentage: 50 }];

			const result = validateCoveragePercentages(mappings);

			expect(result.is_valid).toBe(true);
			expect(result.total_percentage).toBe(50);
			expect(result.error_message).toBeUndefined();
		});

		it('should handle empty mappings array', () => {
			const mappings: { coverage_percentage?: number }[] = [];

			const result = validateCoveragePercentages(mappings);

			expect(result.is_valid).toBe(true);
			expect(result.total_percentage).toBe(0);
			expect(result.error_message).toBeUndefined();
		});
	});

	describe('getWbsItemStatus', () => {
		it('should return unmapped for items without mappings', () => {
			const status = getWbsItemStatus(0, false);
			expect(status).toBe('unmapped');
		});

		it('should return unmapped for items with 0% coverage', () => {
			const status = getWbsItemStatus(0, true);
			expect(status).toBe('unmapped');
		});

		it('should return partial for items with less than 100% coverage', () => {
			const status = getWbsItemStatus(75, true);
			expect(status).toBe('partial');
		});

		it('should return complete for items with exactly 100% coverage', () => {
			const status = getWbsItemStatus(100, true);
			expect(status).toBe('complete');
		});

		it('should return over_allocated for items with more than 100% coverage', () => {
			const status = getWbsItemStatus(120, true);
			expect(status).toBe('over_allocated');
		});
	});

	describe('getStatusColor', () => {
		it('should return correct colors for each status', () => {
			expect(getStatusColor('complete')).toBe('text-green-600 bg-green-50');
			expect(getStatusColor('partial')).toBe('text-yellow-600 bg-yellow-50');
			expect(getStatusColor('over_allocated')).toBe('text-red-600 bg-red-50');
			expect(getStatusColor('unmapped')).toBe('text-gray-600 bg-gray-50');
		});

		it('should return default color for unknown status', () => {
			// @ts-expect-error Testing invalid status
			expect(getStatusColor('invalid')).toBe('text-gray-600 bg-gray-50');
		});
	});

	describe('getStatusLabel', () => {
		it('should return correct labels for each status', () => {
			expect(getStatusLabel('complete')).toBe('Complete');
			expect(getStatusLabel('partial')).toBe('Partial');
			expect(getStatusLabel('over_allocated')).toBe('Over-allocated');
			expect(getStatusLabel('unmapped')).toBe('Unmapped');
		});

		it('should return default label for unknown status', () => {
			// @ts-expect-error Testing invalid status
			expect(getStatusLabel('invalid')).toBe('Unknown');
		});
	});
});
