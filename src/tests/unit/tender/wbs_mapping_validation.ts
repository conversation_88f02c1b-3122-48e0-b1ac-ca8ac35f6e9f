import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database, Json } from '$lib/database.types';

export type WbsMappingValidationResult = {
	wbs_library_item_id: string;
	wbs_code: string;
	wbs_description: string;
	total_coverage_percentage: number;
	total_coverage_amount: number;
	budget_amount: number;
	status: string;
	coverage_details: Json;
};

export type ProjectMappingCompleteness = {
	total_wbs_items: number;
	mapped_items: number;
	complete_items: number;
	partial_items: number;
	over_allocated_items: number;
	unmapped_items: number;
	overall_completion_percentage: number;
	budget_coverage_percentage: number;
	total_budget_amount: number;
	total_mapped_amount: number;
};

/**
 * Validates WBS mapping coverage for a specific WBS item across all tenders in a project
 */
export async function validateWbsItemCoverage(
	supabase: SupabaseClient<Database>,
	projectId: string,
	wbsLibraryItemId: string,
): Promise<WbsMappingValidationResult | null> {
	const { data, error } = await supabase.rpc('validate_wbs_item_coverage', {
		project_id_param: projectId,
		wbs_library_item_id_param: wbsLibraryItemId,
	});

	if (error) {
		throw new Error(`Failed to validate WBS item coverage: ${error.message}`);
	}

	return data?.[0] || null;
}

/**
 * Validates WBS mapping completeness for an entire project
 */
export async function validateProjectMappingCompleteness(
	supabase: SupabaseClient<Database>,
	projectId: string,
): Promise<ProjectMappingCompleteness | null> {
	const { data, error } = await supabase.rpc('validate_project_mapping_completeness', {
		project_id_param: projectId,
	});

	if (error) {
		throw new Error(`Failed to validate project mapping completeness: ${error.message}`);
	}

	return data?.[0] || null;
}

/**
 * Validates coverage for a specific tender line item to ensure it doesn't exceed 100%
 */
export async function validateLineItemCoverage(
	supabase: SupabaseClient<Database>,
	lineItemId: string,
): Promise<{
	is_valid: boolean;
	total_coverage_percentage: number;
	error_message?: string;
} | null> {
	const { data, error } = await supabase.rpc('validate_line_item_coverage', {
		line_item_id_param: lineItemId,
	});

	if (error) {
		throw new Error(`Failed to validate line item coverage: ${error.message}`);
	}

	return data?.[0] || null;
}

/**
 * Gets WBS mapping validation results for all items in a project
 */
export async function getProjectWbsMappingValidation(
	supabase: SupabaseClient<Database>,
	projectId: string,
): Promise<WbsMappingValidationResult[]> {
	const { data, error } = await supabase.rpc('get_project_wbs_mapping_validation', {
		project_id_param: projectId,
	});

	if (error) {
		throw new Error(`Failed to get project WBS mapping validation: ${error.message}`);
	}

	return data || [];
}

/**
 * Client-side validation for coverage percentages
 */
export function validateCoveragePercentages(mappings: { coverage_percentage?: number }[]): {
	is_valid: boolean;
	total_percentage: number;
	error_message?: string;
} {
	const total = mappings.reduce((sum, mapping) => {
		return sum + (mapping.coverage_percentage || 0);
	}, 0);

	const is_valid = total <= 100;
	const error_message = is_valid ? undefined : `Total coverage (${total}%) exceeds 100%`;

	return {
		is_valid,
		total_percentage: total,
		error_message,
	};
}

/**
 * Determines the status of a WBS item based on coverage
 */
export function getWbsItemStatus(
	totalCoveragePercentage: number,
	hasMappings: boolean,
): 'complete' | 'partial' | 'over_allocated' | 'unmapped' {
	if (!hasMappings) return 'unmapped';
	if (totalCoveragePercentage === 0) return 'unmapped';
	if (totalCoveragePercentage < 100) return 'partial';
	if (totalCoveragePercentage === 100) return 'complete';
	return 'over_allocated';
}

/**
 * Gets status color for UI display
 */
export function getStatusColor(
	status: 'complete' | 'partial' | 'over_allocated' | 'unmapped',
): string {
	switch (status) {
		case 'complete':
			return 'text-green-600 bg-green-50';
		case 'partial':
			return 'text-yellow-600 bg-yellow-50';
		case 'over_allocated':
			return 'text-red-600 bg-red-50';
		case 'unmapped':
			return 'text-gray-600 bg-gray-50';
		default:
			return 'text-gray-600 bg-gray-50';
	}
}

/**
 * Gets status label for UI display
 */
export function getStatusLabel(
	status: 'complete' | 'partial' | 'over_allocated' | 'unmapped',
): string {
	switch (status) {
		case 'complete':
			return 'Complete';
		case 'partial':
			return 'Partial';
		case 'over_allocated':
			return 'Over-allocated';
		case 'unmapped':
			return 'Unmapped';
		default:
			return 'Unknown';
	}
}
