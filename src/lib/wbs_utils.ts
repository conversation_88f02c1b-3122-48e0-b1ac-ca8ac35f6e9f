import { stratify } from 'd3-hierarchy';
import { wbsCodeComparator } from './budget_utils';
import type { WbsItemTree, WbsLibraryItemWithId } from './schemas/wbs';

export type WbsBudgetLikeItem = {
	wbs_library_item_id: string;
	parent_item_id: string | null;
	budget_amount?: number | null;
	budget_quantity?: number | null;
};

export type WbsAggregatedBudget = {
	amount: number;
	quantity: number;
};

/**
 * Converts a flat array of WBS items to a hierarchical tree structure
 * @param items Array of WBS items with IDs
 * @returns Array of root WBS items with children arrays
 */
export function buildWbsItemTree(items: WbsLibraryItemWithId[]): WbsItemTree[] {
	const itemMap = new Map<string, WbsItemTree>();
	const rootItems: WbsItemTree[] = [];

	// First pass: Create all nodes with empty children arrays
	items.forEach((item) => {
		itemMap.set(item.wbs_library_item_id, { ...item, children: [] });
	});

	// Second pass: Create parent-child relationships for all levels
	items.forEach((item) => {
		const node = itemMap.get(item.wbs_library_item_id);

		if (!node) return;

		if (item.parent_item_id === null || item.parent_item_id === undefined) {
			// This is a root item
			rootItems.push(node);
		} else {
			// Add this item to its parent's children
			const parent = itemMap.get(item.parent_item_id);
			if (parent) {
				parent.children.push(node);
			} else {
				// If parent doesn't exist (which shouldn't happen with valid data),
				// add to root items as a fallback
				rootItems.push(node);
			}
		}
	});

	// Sort children by code at each level
	const sortNodeChildren = (nodes: WbsItemTree[]) => {
		// Sort current level
		nodes.sort((a, b) => wbsCodeComparator(a.code, b.code));

		// Recursively sort children
		for (const node of nodes) {
			if (node.children.length > 0) {
				sortNodeChildren(node.children);
			}
		}
	};

	// Sort the entire tree
	sortNodeChildren(rootItems);

	return rootItems;
}

export function buildAggregatedBudgetMap<T extends WbsBudgetLikeItem>(
	items: T[],
): Map<string, WbsAggregatedBudget> {
	const aggregates = new Map<string, WbsAggregatedBudget>();
	if (items.length === 0) {
		return aggregates;
	}

	const toNumber = (value: number | null | undefined): number => {
		if (value === null || value === undefined) return 0;
		const numeric = Number(value);
		return Number.isFinite(numeric) ? numeric : 0;
	};

	const idSet = new Set(items.map((item) => item.wbs_library_item_id));

	type AggregationNode = {
		id: string;
		parentId: string | null;
		rawAmount: number;
		rawQuantity: number;
	};

	const nodes: AggregationNode[] = items.map((item) => {
		let parentId = item.parent_item_id ?? null;
		if (parentId && !idSet.has(parentId)) {
			parentId = null;
		}

		return {
			id: item.wbs_library_item_id,
			parentId,
			rawAmount: toNumber(item.budget_amount),
			rawQuantity: toNumber(item.budget_quantity),
		};
	});

	const rootCount = nodes.reduce((count, node) => (node.parentId ? count : count + 1), 0);
	const syntheticRootId = '__aggregated_budget_root__';

	const stratifyInput: AggregationNode[] =
		rootCount > 1
			? [
					{ id: syntheticRootId, parentId: null, rawAmount: 0, rawQuantity: 0 },
					...nodes.map((node) => ({
						...node,
						parentId: node.parentId ?? syntheticRootId,
					})),
				]
			: nodes;

	const stratifyFn = stratify<AggregationNode>()
		.id((d) => d.id)
		.parentId((d) => (d.parentId === null ? undefined : d.parentId));

	const root = stratifyFn(stratifyInput);

	root.eachAfter((node) => {
		const nodeId = node.id ?? node.data.id;
		if (!nodeId) {
			return;
		}
		let amount = node.data.rawAmount;
		let quantity = node.data.rawQuantity;

		if (node.children) {
			for (const child of node.children) {
				const childId = child.id ?? child.data.id;
				if (!childId) {
					continue;
				}
				const childTotals = aggregates.get(childId);
				if (childTotals) {
					amount += childTotals.amount;
					quantity += childTotals.quantity;
				}
			}
		}

		if (nodeId !== syntheticRootId) {
			aggregates.set(nodeId, { amount, quantity });
		}
	});

	return aggregates;
}
