export type WbsMappingFormMessage = {
	type: 'success' | 'error';
	text: string;
};

export type CreateWbsMappingForm = {
	wbs_library_item_id: string;
	coverage_percentage?: number | null;
	coverage_quantity?: number | null;
	notes?: string | null;
};

export type EditWbsMappingForm = {
	tender_wbs_mapping_id: string;
	wbs_library_item_id?: string;
	coverage_percentage?: number | null;
	coverage_quantity?: number | null;
	notes?: string | null;
};

export type DeleteWbsMappingForm = {
	tender_wbs_mapping_id: string;
};

export type BulkWbsMappingForm = {
	mappings: CreateWbsMappingForm[];
};
