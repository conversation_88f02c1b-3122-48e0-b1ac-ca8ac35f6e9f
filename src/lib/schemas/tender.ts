import short from 'short-uuid';
import { z } from 'zod';

// Short UUID utilities for tender-related entities
const translator = short();
export const tenderShortId = translator.fromUUID;
export const tenderUUID = translator.toUUID;
export const lineItemShortId = translator.fromUUID;
export const lineItemUUID = translator.toUUID;
export const revisionShortId = translator.fromUUID;
export const revisionUUID = translator.toUUID;

// Tender status enum values (matching database enum)
export const tenderStatuses = ['submitted', 'under_review', 'selected', 'rejected'] as const;

// Normalization type enum values
export const normalizationTypes = ['amount', 'percentage'] as const;

// Main tender schema
export const tenderSchema = z.object({
	tender_name: z.string().min(1, 'Tender name is required'),
	description: z.string().optional().nullable(),
	vendor_id: z.string().min(1, 'Please select a vendor'),
	submission_date: z.string().min(1, 'Submission date is required'),
	currency_code: z.string().min(3).max(3).default('SEK'),
	status: z.enum(tenderStatuses).default('submitted'),
	notes: z.string().optional().nullable(),
});

// Tender revision schema
export const tenderRevisionSchema = z.object({
	tender_id: z.uuid(),
	revision_number: z.number().int().min(1),
	is_current: z.boolean().default(false),
	revision_notes: z.string().optional().nullable(),
});

// Tender line item schema with flexible normalization
export const tenderLineItemSchema = z
	.object({
		tender_revision_id: z.uuid(),
		line_number: z.number().int().min(1),
		description: z.string().min(1, 'Description is required'),
		quantity: z.number().positive().optional().nullable(),
		unit: z.string().optional().nullable(),
		material_rate: z.number().min(0).optional().nullable(),
		labor_rate: z.number().min(0).optional().nullable(),
		productivity_factor: z.number().positive().optional().nullable(),
		unit_rate: z.number().min(0).optional().nullable(),
		unit_rate_manual_override: z.boolean().optional().default(false),
		subtotal: z.number().min(0).optional().nullable(),
		normalization_type: z.enum(normalizationTypes).default('amount'),
		normalization_amount: z.number().min(0).optional().nullable(),
		normalization_percentage: z.number().min(0).max(100).optional().nullable(),
		notes: z.string().optional().nullable(),
	})
	.refine(
		(data) => {
			// Validate normalization input based on type
			if (data.normalization_type === 'amount') {
				return data.normalization_amount !== null && data.normalization_amount !== undefined;
			} else if (data.normalization_type === 'percentage') {
				return (
					data.normalization_percentage !== null && data.normalization_percentage !== undefined
				);
			}
			return true;
		},
		{
			message: 'Normalization value is required based on selected type',
			path: ['normalization_amount'],
		},
	);

// WBS mapping schema
export const tenderWbsMappingSchema = z.object({
	tender_line_item_id: z.uuid(),
	wbs_library_item_id: z.uuid('Please select a WBS item'),
	coverage_percentage: z.number().min(0.01).max(100).default(100),
	coverage_quantity: z.number().positive().optional().nullable(),
	notes: z.string().optional().nullable(),
});

// Scoring criteria schema
export const tenderScoringCriteriaSchema = z.object({
	project_id: z.uuid(),
	criteria_name: z.string().min(1, 'Criteria name is required'),
	description: z.string().optional().nullable(),
	weight: z.number().positive().default(1),
	max_score: z.number().positive().default(10),
	is_active: z.boolean().default(true),
});

// Tender score schema
export const tenderScoreSchema = z.object({
	tender_id: z.uuid(),
	tender_scoring_criteria_id: z.uuid(),
	score: z.number().min(0),
	comments: z.string().optional().nullable(),
});

// Budget transfer schema
export const budgetTransferSchema = z.object({
	from_wbs_library_item_id: z.uuid('Please select source WBS item'),
	to_wbs_library_item_id: z.uuid('Please select target WBS item'),
	transfer_amount: z.number().positive('Transfer amount must be positive'),
	transfer_reason: z.string().optional().nullable(),
});

// Work package conversion schema
export const tenderWorkPackageSchema = z.object({
	tender_id: z.uuid(),
	work_package_name: z.string().min(1, 'Work package name is required'),
	work_package_description: z.string().optional().nullable(),
	conversion_date: z.date(),
	conversion_notes: z.string().optional().nullable(),
});

// Type definitions
export type TenderSchema = z.infer<typeof tenderSchema>;
export type TenderRevisionSchema = z.infer<typeof tenderRevisionSchema>;
export type TenderLineItemSchema = z.infer<typeof tenderLineItemSchema>;
export type TenderWbsMappingSchema = z.infer<typeof tenderWbsMappingSchema>;
export type TenderScoringCriteriaSchema = z.infer<typeof tenderScoringCriteriaSchema>;
export type TenderScoreSchema = z.infer<typeof tenderScoreSchema>;
export type BudgetTransferSchema = z.infer<typeof budgetTransferSchema>;
export type TenderWorkPackageSchema = z.infer<typeof tenderWorkPackageSchema>;

// Form schemas for creating/editing (without IDs)
export const createTenderSchema = tenderSchema;
export const editTenderSchema = tenderSchema.partial().required({ tender_name: true });

const optionalWbsId = z
	.uuid('Please select a valid WBS item')
	.or(z.literal(''))
	.or(z.null())
	.transform((value) => (value ? value : null))
	.optional();

export const createLineItemSchema = tenderLineItemSchema.omit({ tender_revision_id: true }).extend({
	primary_wbs_library_item_id: optionalWbsId,
});

export const editLineItemSchema = tenderLineItemSchema
	.extend({
		tender_line_item_id: z.uuid(),
		primary_wbs_library_item_id: optionalWbsId,
	})
	.omit({ tender_revision_id: true })
	.partial()
	.required({ description: true, tender_line_item_id: true });

export const createWbsMappingSchema = tenderWbsMappingSchema.omit({ tender_line_item_id: true });
const baseEditWbsMappingSchema = tenderWbsMappingSchema
	.omit({ tender_line_item_id: true })
	.partial();

export const editWbsMappingSchema = baseEditWbsMappingSchema.extend({
	tender_wbs_mapping_id: z.uuid('Mapping ID is required'),
});

export const deleteWbsMappingSchema = z.object({
	tender_wbs_mapping_id: z.uuid('Mapping ID is required'),
});

export const createScoringCriteriaSchema = tenderScoringCriteriaSchema.omit({ project_id: true });
export const editScoringCriteriaSchema = tenderScoringCriteriaSchema
	.omit({ project_id: true })
	.partial()
	.required({ criteria_name: true });

export const createTenderScoreSchema = tenderScoreSchema.omit({ tender_id: true });
export const editTenderScoreSchema = tenderScoreSchema.omit({ tender_id: true }).partial();

export const createBudgetTransferSchema = budgetTransferSchema;

// Bulk operations schemas
export const bulkLineItemSchema = z.object({
	line_items: z.array(createLineItemSchema).min(1, 'At least one line item is required'),
});

export const bulkWbsMappingSchema = z.object({
	mappings: z.array(createWbsMappingSchema).min(1, 'At least one mapping is required'),
});

// Normalization update schema
export const updateNormalizationSchema = z
	.object({
		tender_line_item_id: z.uuid(),
		normalization_type: z.enum(normalizationTypes),
		normalization_amount: z.number().min(0).optional().nullable(),
		normalization_percentage: z.number().min(0).max(100).optional().nullable(),
	})
	.refine(
		(data) => {
			if (data.normalization_type === 'amount') {
				return data.normalization_amount !== null && data.normalization_amount !== undefined;
			} else if (data.normalization_type === 'percentage') {
				return (
					data.normalization_percentage !== null && data.normalization_percentage !== undefined
				);
			}
			return true;
		},
		{
			message: 'Normalization value is required based on selected type',
			path: ['normalization_amount'],
		},
	);

export type UpdateNormalizationSchema = z.infer<typeof updateNormalizationSchema>;

// Database type imports and extended types
import type { Database, Tables } from '$lib/database.types';

// Database table types
export type TenderRow = Tables<'tender'>;
export type TenderRevisionRow = Tables<'tender_revision'>;
export type TenderLineItemRow = Tables<'tender_line_item'>;
export type TenderWbsMappingRow = Tables<'tender_wbs_mapping'>;
export type TenderScoringCriteriaRow = Tables<'tender_scoring_criteria'>;
export type TenderScoreRow = Tables<'tender_score'>;
export type BudgetTransferRow = Tables<'budget_transfer'>;
export type TenderWorkPackageRow = Tables<'tender_work_package'>;

// RPC function return types
export type ProjectTendersData = Database['public']['Functions']['get_project_tenders']['Returns'];
export type TenderComparisonData =
	Database['public']['Functions']['get_tender_comparison_data']['Returns'];

// Extended types with relationships
export type TenderWithDetails = TenderRow & {
	vendor: Pick<Tables<'vendor'>, 'vendor_id' | 'name' | 'currency'>;
	current_revision?: TenderRevisionRow & {
		line_items: (TenderLineItemRow & {
			tender_wbs_mapping: (TenderWbsMappingRow & {
				wbs_library_item: Pick<Tables<'wbs_library_item'>, 'code' | 'description'>;
			})[];
		})[];
	};
	scores?: (TenderScoreRow & {
		criteria: TenderScoringCriteriaRow;
	})[];
};

export type TenderLineItemWithMappings = TenderLineItemRow & {
	tender_wbs_mapping: (TenderWbsMappingRow & {
		wbs_library_item: Pick<Tables<'wbs_library_item'>, 'code' | 'description' | 'level'>;
	})[];
};

export type WbsItemWithBudget = Tables<'wbs_library_item'> & {
	budget_amount?: number;
	budget_quantity?: number | null;
	budget_unit_rate?: number | null;
	unit?: string | null;
};

// Tender comparison types
export type TenderLineData = {
	tender_id: string;
	vendor_name: string;
	tender_name: string;
	line_item_id: string;
	line_description: string;
	quantity?: number;
	unit?: string;
	unit_rate?: number;
	subtotal?: number;
	coverage_percentage: number;
	coverage_quantity?: number;
	normalization_type: 'amount' | 'percentage';
	normalization_amount?: number;
	normalization_percentage?: number;
};

export type ComparisonWbsItem = {
	wbs_library_item_id: string;
	wbs_code: string;
	wbs_description: string;
	wbs_level: number;
	parent_item_id?: string;
	budget_amount: number;
	budget_quantity?: number;
	budget_unit_rate?: number;
	tender_data: TenderLineData[];
};

// Form data types for components
export type TenderFormData = TenderSchema & {
	project_id: string;
};

export type LineItemFormData = TenderLineItemSchema & {
	tender_wbs_mapping?: TenderWbsMappingSchema[];
};

export type ScoringFormData = {
	tender_id: string;
	scores: TenderScoreSchema[];
};

// Status and enum types
export type TenderStatus = Database['public']['Enums']['tender_status'];
export type NormalizationType = Database['public']['Enums']['normalization_type'];

// Utility types for forms
export type CreateTenderFormData = z.infer<typeof createTenderSchema> & {
	project_id: string;
};

export type EditTenderFormData = z.infer<typeof editTenderSchema> & {
	tender_id: string;
};

export type CreateLineItemFormData = z.infer<typeof createLineItemSchema> & {
	tender_revision_id: string;
};

export type EditLineItemFormData = z.infer<typeof editLineItemSchema> & {
	tender_line_item_id: string;
};
