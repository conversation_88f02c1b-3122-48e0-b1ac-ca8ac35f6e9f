import { clsx, type ClassValue } from 'clsx';
import { format } from 'date-fns';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChild<T> = T extends { child?: any } ? Omit<T, 'child'> : T;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChildren<T> = T extends { children?: any } ? Omit<T, 'children'> : T;
export type WithoutChildrenOrChild<T> = WithoutChildren<WithoutChild<T>>;
export type WithElementRef<T, U extends HTMLElement = HTMLElement> = T & { ref?: U | null };

export const SUPABASE_SYMBOL = Symbol('SUPABASE');

/**
 * Format a date string into a more readable format
 * @param dateString ISO date string
 * @returns Formatted date string (YYYY-MM-DD)
 */
export function formatDate(dateString: string, style = 'yyyy-MM-dd') {
	if (!dateString) return '';
	return format(new Date(dateString), style);
}

export type FormatCurrencyOptions = {
	locale?: string;
	currency?: string;
	symbol?: string | null;
	symbolPosition?: 'before' | 'after' | 'left' | 'right';
	minimumFractionDigits?: number;
	maximumFractionDigits?: number;
	fallback?: string;
	useGrouping?: boolean;
};

/**
 * Format numeric values consistently across the app.
 * Supports either ISO currency codes via `Intl.NumberFormat` or custom symbols/positions.
 */
export function formatCurrency(
	value: number | null | undefined,
	options: FormatCurrencyOptions = {},
): string {
	const hasLocaleOption = Object.prototype.hasOwnProperty.call(options, 'locale');
	const hasCurrencyOption = Object.prototype.hasOwnProperty.call(options, 'currency');
	const hasSymbolOption = Object.prototype.hasOwnProperty.call(options, 'symbol');
	const hasMinDigitsOption = Object.prototype.hasOwnProperty.call(options, 'minimumFractionDigits');
	const hasMaxDigitsOption = Object.prototype.hasOwnProperty.call(options, 'maximumFractionDigits');
	const hasFallbackOption = Object.prototype.hasOwnProperty.call(options, 'fallback');
	const hasUseGroupingOption = Object.prototype.hasOwnProperty.call(options, 'useGrouping');

	const {
		locale,
		currency,
		symbol,
		symbolPosition = 'before',
		minimumFractionDigits,
		maximumFractionDigits = 2,
		useGrouping = true,
	} = options;
	const fallback = hasFallbackOption ? (options.fallback ?? '') : '';

	if (value === null || value === undefined || Number.isNaN(value)) {
		return fallback;
	}

	const resolvedSymbol = typeof symbol === 'string' ? symbol.trim() : (symbol ?? '');
	const hasResolvedSymbol = resolvedSymbol !== '';

	const resolvedLocale = locale ?? (currency ? 'sv-SE' : 'en-GB');
	const shouldForceZeroFractions =
		!currency &&
		!hasResolvedSymbol &&
		!hasSymbolOption &&
		!hasMinDigitsOption &&
		!hasMaxDigitsOption &&
		!hasLocaleOption &&
		!hasUseGroupingOption &&
		!hasFallbackOption &&
		!hasCurrencyOption;
	const resolvedMinFraction =
		minimumFractionDigits !== undefined
			? minimumFractionDigits
			: shouldForceZeroFractions
				? 0
				: undefined;
	const resolvedMaxFraction =
		maximumFractionDigits !== undefined
			? maximumFractionDigits
			: shouldForceZeroFractions
				? 0
				: resolvedMinFraction;

	const baseOptions: Intl.NumberFormatOptions = {
		useGrouping,
	};

	if (resolvedMinFraction !== undefined) {
		baseOptions.minimumFractionDigits = resolvedMinFraction;
	}

	if (resolvedMaxFraction !== undefined) {
		baseOptions.maximumFractionDigits = resolvedMaxFraction;
	}

	if (currency) {
		return new Intl.NumberFormat(resolvedLocale, {
			style: 'currency',
			currency,
			...baseOptions,
		}).format(value);
	}

	const formatted = new Intl.NumberFormat(resolvedLocale, baseOptions).format(value);

	if (!hasResolvedSymbol) {
		return formatted;
	}

	const placeAfter = symbolPosition === 'after' || symbolPosition === 'right';
	return placeAfter ? `${formatted} ${resolvedSymbol}` : `${resolvedSymbol}${formatted}`;
}

// Format uncertainty
export function formatUncertaintyPercentage(value: number | null | undefined): string {
	if (value === null || value === undefined) return '';
	return `+/- ${value}%`;
}

// Format percentage
export function formatPercentage(value: number | null | undefined): string {
	if (value === null || value === undefined) return '';
	return `${(100 * value).toLocaleString(undefined, {
		minimumFractionDigits: 1,
		maximumFractionDigits: 1,
	})}%`;
}

export function capitalizeFirstLetter(s: string) {
	return String(s).charAt(0).toUpperCase() + String(s).slice(1);
}
