<script lang="ts">
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { formatCurrency } from '$lib/utils';
	import type { WbsItemWithBudget } from '$lib/schemas/tender';
	import type { BudgetTransfer } from './budget-transfer.types';

	interface Props {
		availableWbsItems: WbsItemWithBudget[];
		existingTransfers?: BudgetTransfer[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
	}

	let {
		availableWbsItems,
		existingTransfers = [],
		currencySymbol = 'kr',
		symbolPosition = 'after',
	}: Props = $props();

	function getWbsItem(itemId: string) {
		return availableWbsItems.find((item) => item.wbs_library_item_id === itemId);
	}
</script>

{#if existingTransfers.length > 0}
	<Table class="min-w-[640px]">
		<TableHeader>
			<TableRow>
				<TableHead class="w-[200px]">Transfer From</TableHead>
				<TableHead class="w-[200px]">Transfer To</TableHead>
				<TableHead class="text-right">Amount</TableHead>
				<TableHead class="text-right">Created</TableHead>
				<TableHead>Reason</TableHead>
			</TableRow>
		</TableHeader>
		<TableBody>
			{#each existingTransfers as transfer (transfer.budget_transfer_id)}
				{@const fromItem = getWbsItem(transfer.from_wbs_library_item_id)}
				{@const toItem = getWbsItem(transfer.to_wbs_library_item_id)}

				<TableRow>
					<TableCell class="align-top whitespace-normal">
						<div class="flex flex-col gap-0.5">
							<div class="font-mono text-sm font-medium text-gray-900">
								{fromItem?.code || 'Unknown'}
							</div>
							{#if fromItem?.description}
								<div class="text-xs text-gray-600">{fromItem.description}</div>
							{/if}
						</div>
					</TableCell>
					<TableCell class="align-top whitespace-normal">
						<div class="flex flex-col gap-0.5">
							<div class="font-mono text-sm font-medium text-gray-900">
								{toItem?.code || 'Unknown'}
							</div>
							{#if toItem?.description}
								<div class="text-xs text-gray-600">{toItem.description}</div>
							{/if}
						</div>
					</TableCell>
					<TableCell class="text-right font-semibold">
						{formatCurrency(transfer.transfer_amount, {
							symbol: currencySymbol,
							symbolPosition,
							fallback: '-',
						})}
					</TableCell>
					<TableCell class="text-right text-xs text-gray-500">
						{new Date(transfer.created_at).toLocaleDateString()}
					</TableCell>
					<TableCell>
						{#if transfer.reason}
							<div class="max-w-[240px] truncate text-xs text-gray-600" title={transfer.reason}>
								{transfer.reason}
							</div>
						{:else}
							<span class="text-xs text-gray-400">—</span>
						{/if}
					</TableCell>
				</TableRow>
			{/each}
		</TableBody>
	</Table>
{:else}
	<div class="py-8 text-center text-gray-500">
		<p class="text-sm">No budget transfers yet</p>
		<p class="text-xs">Create transfers to reallocate budget between WBS items</p>
	</div>
{/if}
