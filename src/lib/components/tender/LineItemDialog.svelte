<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Form from '$lib/components/ui/form';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import NormalizationInput from '$lib/components/tender/NormalizationInput.svelte';
	import { cn, formatCurrency } from '$lib/utils';
	import { buildAggregatedBudgetMap } from '$lib/wbs_utils';
	import CheckIcon from '@lucide/svelte/icons/check';
	import ChevronsUpDownIcon from '@lucide/svelte/icons/chevrons-up-down';
	import type { WbsItemTree } from '$lib/schemas/wbs';
	import type { WbsItemWithBudget } from '$lib/schemas/tender';
	import { useId } from 'bits-ui';
	import { tick } from 'svelte';
	import type { Infer, SuperForm } from 'sveltekit-superforms';
	type HiddenField = { name: string; value: string | number | null | undefined };
	type CreateLineItemSchema = (typeof import('$lib/schemas/tender'))['createLineItemSchema'];
	type EditLineItemSchema = (typeof import('$lib/schemas/tender'))['editLineItemSchema'];
	type LineItemForm = SuperForm<Infer<CreateLineItemSchema>> | SuperForm<Infer<EditLineItemSchema>>;

	let {
		open = $bindable(false),
		title = 'Line Item',
		description = '',
		submitLabel = 'Save Line Item',
		action = '?/create',
		form,
		currencySymbol,
		currencySymbolPosition,
		hiddenFields = [],
		wbsItems = [],
		wbsItemsTree = [],
	} = $props<{
		open?: boolean;
		title?: string;
		description?: string;
		submitLabel?: string;
		action?: string;
		form: LineItemForm;
		currencySymbol: string;
		currencySymbolPosition: 'before' | 'after';
		hiddenFields?: HiddenField[];
		wbsItems?: WbsItemWithBudget[];
		wbsItemsTree?: WbsItemTree[];
	}>();

	const { form: formData, enhance } = form;

	function coerceToNumber(value: unknown) {
		if (typeof value === 'number') {
			return Number.isFinite(value) ? value : 0;
		}

		if (typeof value === 'string' && value.trim() !== '') {
			const parsed = Number(value);
			return Number.isFinite(parsed) ? parsed : 0;
		}

		return 0;
	}

	const EPSILON = 0.0001;

	function setDerivedNumber(field: string, value: number) {
		if (!Number.isFinite(value)) {
			return;
		}

		const current = coerceToNumber(($formData as Record<string, unknown>)[field]);
		if (Math.abs(current - value) > EPSILON) {
			($formData as Record<string, unknown>)[field] = value;
		}
	}

	const unitRateCalculation = $derived.by(() => {
		const materialRate = coerceToNumber($formData.material_rate);
		const laborRate = coerceToNumber($formData.labor_rate);
		const productivityRaw = coerceToNumber($formData.productivity_factor);
		const productivity = productivityRaw > 0 ? productivityRaw : 1;

		return {
			materialRate,
			laborRate,
			productivity,
			productivityRaw,
			result: materialRate + laborRate / productivity,
		};
	});

	const autoUnitRate = $derived.by(() => unitRateCalculation.result);

	const calculationSummary = $derived.by(() => {
		const { materialRate, laborRate, productivity, result } = unitRateCalculation;
		return {
			material: materialRate,
			labor: laborRate,
			productivity: productivity,
			result: result,
		};
	});

	$effect(() => {
		if (!$formData.unit_rate_manual_override) {
			setDerivedNumber('unit_rate', autoUnitRate);
		}
	});

	const effectiveUnitRate = $derived.by(() => {
		if ($formData.unit_rate_manual_override) {
			return coerceToNumber($formData.unit_rate);
		}

		return autoUnitRate;
	});

	const subtotal = $derived.by(() => {
		const quantity = coerceToNumber($formData.quantity);
		return quantity * effectiveUnitRate;
	});

	$effect(() => {
		setDerivedNumber('subtotal', subtotal);
	});

	function handleManualOverrideChange(event: Event) {
		const target = event.currentTarget as HTMLInputElement;
		const checked = target.checked;
		$formData.unit_rate_manual_override = checked;

		if (!checked) {
			setDerivedNumber('unit_rate', autoUnitRate);
		}
	}

	const aggregatedBudgets = $derived(buildAggregatedBudgetMap(wbsItems));

	function flattenWbsTree(items: WbsItemTree[], depth = 0): Array<WbsItemTree & { depth: number }> {
		const result: Array<WbsItemTree & { depth: number }> = [];

		for (const item of items) {
			result.push({ ...item, depth });
			if (item.children.length > 0) {
				result.push(...flattenWbsTree(item.children, depth + 1));
			}
		}

		return result;
	}

	const flatWbsItems = $derived(flattenWbsTree(wbsItemsTree));

	const flatWbsOptions = $derived(
		flatWbsItems.map((item) => ({
			value: item.wbs_library_item_id,
			label: `${item.code} – ${item.description}`,
			code: item.code,
			description: item.description,
			depth: item.depth,
			hasChildren: item.children.length > 0,
		})),
	);

	let wbsOpen = $state(false);
	const wbsTriggerId = useId();

	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	function getIndentStyle(depth: number) {
		return `padding-left: ${depth * 1.5}rem`;
	}

	const selectedWbsItem = $derived.by(() => {
		const selectedId = $formData.primary_wbs_library_item_id;
		if (!selectedId) {
			return null;
		}
		return (
			wbsItems.find((item: WbsItemWithBudget) => item.wbs_library_item_id === selectedId) ?? null
		);
	});

	const budgetSummary = $derived.by(() => {
		if (!selectedWbsItem) {
			return null;
		}
		const totals = aggregatedBudgets.get(selectedWbsItem.wbs_library_item_id);
		const amount = totals?.amount ?? 0;
		const quantity = totals?.quantity ?? 0;
		const subtotalValue = subtotal;
		if (amount <= 0 || subtotalValue <= 0) {
			return {
				amount,
				quantity,
				delta: null as { difference: number; percent: number } | null,
			};
		}
		const difference = subtotalValue - amount;
		const percent = (difference / amount) * 100;
		return {
			amount,
			quantity,
			delta: {
				difference,
				percent,
			},
		};
	});

	const suggestedQuantity = $derived.by(() => {
		if (!budgetSummary) {
			return null;
		}
		return budgetSummary.quantity > 0 ? budgetSummary.quantity : null;
	});

	const budgetDeltaClass = $derived.by(() => {
		const delta = budgetSummary?.delta;
		if (!delta || Math.abs(delta.percent) < 0.01) {
			return 'text-muted-foreground';
		}
		return delta.difference > 0 ? 'text-red-600' : 'text-emerald-600';
	});

	const suggestedDescription = $derived.by(() => {
		if (!selectedWbsItem) {
			return '';
		}

		return selectedWbsItem.description;
	});

	function applySuggestedDescription() {
		if (!suggestedDescription) {
			return;
		}
		$formData.description = suggestedDescription;
	}

	function applySuggestedQuantity() {
		if (suggestedQuantity === null) {
			return;
		}
		$formData.quantity = suggestedQuantity;
	}

	function clearPrimaryWbsSelection() {
		$formData.primary_wbs_library_item_id = null;
	}
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="max-h-[90vh] max-w-5xl overflow-auto">
		<form method="POST" {action} use:enhance class="space-y-6">
			<Dialog.Header>
				<Dialog.Title>{title}</Dialog.Title>
				{#if description}
					<Dialog.Description>{description}</Dialog.Description>
				{/if}
			</Dialog.Header>

			{#if hiddenFields.length}
				{#each hiddenFields as field (field.name)}
					<input type="hidden" name={field.name} value={field.value ?? ''} />
				{/each}
			{/if}

			{#if wbsItems.length}
				<div
					class="border-muted-foreground/30 bg-muted/20 space-y-3 rounded-lg border border-dashed p-4"
				>
					<div class="flex flex-wrap items-start justify-between gap-2">
						<div>
							<p class="text-foreground text-sm font-medium">Primary WBS Mapping</p>
							<p class="text-muted-foreground text-xs">
								Optionally link this line item to a WBS code for automatic mapping.
							</p>
						</div>
						{#if $formData.primary_wbs_library_item_id}
							<Button
								type="button"
								variant="ghost"
								size="sm"
								class="text-xs"
								onclick={clearPrimaryWbsSelection}
							>
								Clear
							</Button>
						{/if}
					</div>

					<Form.Field {form} name="primary_wbs_library_item_id">
						<Popover.Root bind:open={wbsOpen}>
							<Form.Control id={wbsTriggerId}>
								{#snippet children({ props })}
									<Form.Label>Select WBS Item</Form.Label>
									<Popover.Trigger
										type="button"
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full items-start justify-between text-left whitespace-normal',
											'h-auto min-h-[3rem] py-3',
											!$formData.primary_wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										aria-expanded={wbsOpen}
										{...props}
									>
										{#if selectedWbsItem}
											<span class="flex min-w-0 flex-1 flex-col gap-1 text-left">
												<span class="text-muted-foreground font-mono text-[0.7rem] uppercase">
													{selectedWbsItem.code}
												</span>
												<span class="text-foreground text-sm">{selectedWbsItem.description}</span>
											</span>
										{:else}
											<span class="flex-1">Search by code or description</span>
										{/if}
										<ChevronsUpDownIcon class="mt-1 ml-2 h-4 w-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input
										hidden
										name={props.name}
										value={$formData.primary_wbs_library_item_id ?? ''}
									/>
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-[--radix-popover-trigger-width] max-w-[min(calc(100vw-4rem),40rem)] p-0"
							>
								<Command.Root class="overflow-hidden">
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No WBS item found.</Command.Empty>
									<Command.Group class="max-h-60 overflow-y-auto">
										{#each flatWbsOptions as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$formData.primary_wbs_library_item_id = option.value;
													closeAndFocusTrigger(wbsTriggerId, (value) => (wbsOpen = value));
												}}
												class="w-full items-start gap-3 overflow-hidden"
											>
												<div
													style={getIndentStyle(option.depth)}
													class="flex min-w-0 flex-1 items-start gap-2 text-left"
												>
													{#if option.hasChildren}
														<span class="mt-0.5 text-gray-400">📁</span>
													{:else}
														<span class="mt-0.5 text-gray-400">📄</span>
													{/if}
													<div class="flex min-w-0 flex-1 flex-col gap-1">
														<span class="text-muted-foreground font-mono text-xs"
															>{option.code}</span
														>
														<span class="text-foreground text-sm break-words"
															>{option.description}</span
														>
													</div>
												</div>
												<CheckIcon
													class={cn(
														'mt-1 ml-auto h-4 w-4 shrink-0',
														option.value !== $formData.primary_wbs_library_item_id &&
															'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>

					{#if selectedWbsItem}
						<div class="bg-muted/60 grid grid-cols-2 space-y-2 rounded-md p-3 text-xs">
							<div>
								{#if budgetSummary}
									{#if budgetSummary.amount > 0}
										<div class="flex flex-wrap items-center gap-2">
											<span class="text-muted-foreground">Budget:</span>
											<span class="text-foreground font-medium">
												{formatCurrency(budgetSummary.amount, {
													symbol: currencySymbol,
													symbolPosition: currencySymbolPosition,
													fallback: '-',
												})}
											</span>
											{#if budgetSummary.delta}
												<span class={`font-medium ${budgetDeltaClass}`}>
													{budgetSummary.delta.percent >= 0
														? '+'
														: ''}{budgetSummary.delta.percent.toFixed(1)}% ({formatCurrency(
														budgetSummary.delta.difference,
														{
															symbol: currencySymbol,
															symbolPosition: currencySymbolPosition,
														},
													)})
												</span>
											{/if}
										</div>
									{:else}
										<p class="text-muted-foreground">No budget data for this WBS item.</p>
									{/if}
								{/if}
							</div>
							<div class="flex flex-col gap-2">
								{#if suggestedDescription}
									<div class="flex w-full flex-wrap items-center gap-3">
										<Button
											type="button"
											variant="outline"
											class="text-accent-foreground w-full text-xs"
											disabled={$formData.description === suggestedDescription}
											onclick={applySuggestedDescription}
										>
											Use the WBS description
										</Button>
									</div>
								{/if}
								{#if suggestedQuantity !== null}
									<div class="flex w-full flex-wrap items-center gap-3">
										<Button
											type="button"
											variant="outline"
											class="text-accent-foreground w-full text-xs"
											disabled={$formData.quantity === suggestedQuantity}
											onclick={applySuggestedQuantity}
										>
											Use budgeted quantity: {suggestedQuantity.toLocaleString(undefined, {
												maximumFractionDigits: 4,
											})}
										</Button>
									</div>
								{/if}
							</div>
						</div>
					{/if}
				</div>
			{/if}

			<div class="grid gap-4 py-4 md:grid-cols-12">
				<div class="col-span-12 md:col-span-4">
					<Form.Field {form} name="line_number">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Line Number <span class="text-red-500">*</span></Form.Label>
								<Input {...props} type="number" bind:value={$formData.line_number} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12">
					<Form.Field {form} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description <span class="text-red-500">*</span></Form.Label>
								<Textarea
									{...props}
									bind:value={$formData.description}
									placeholder={selectedWbsItem ? suggestedDescription : undefined}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-4">
					<Form.Field {form} name="quantity">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Quantity</Form.Label>
								<Input {...props} type="number" bind:value={$formData.quantity} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-2">
					<Form.Field {form} name="unit">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Unit</Form.Label>
								<Input {...props} bind:value={$formData.unit} placeholder="e.g., m², kg, hours" />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-6">
					<Form.Field {form} name="material_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Material Rate</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.material_rate} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-6">
					<Form.Field {form} name="labor_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Labor Rate</Form.Label>
								<Input {...props} type="number" step="0.01" bind:value={$formData.labor_rate} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-6">
					<Form.Field {form} name="productivity_factor">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Productivity Factor</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.productivity_factor}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-6">
					<Form.Field {form} name="unit_rate">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>
									{$formData.unit_rate_manual_override
										? 'Manual Unit Rate'
										: 'Calculated Unit Rate'}
								</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.unit_rate}
									readonly={!$formData.unit_rate_manual_override}
									class={[!$formData.unit_rate_manual_override && 'opacity-60']}
								/>
								{#if !$formData.unit_rate_manual_override && $formData.labor_rate && $formData.productivity_factor > 0}
									<p class="text-muted-foreground text-xs">
										Calculation: {calculationSummary.material} + {calculationSummary.labor} ÷ {calculationSummary.productivity}
										= {calculationSummary.result}
									</p>
								{/if}
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-4 md:flex md:items-start">
					<Form.Field {form} name="unit_rate_manual_override" class="space-y-0">
						<Form.Control>
							{#snippet children({ props })}
								<label class="inline-flex items-center gap-2 text-sm font-medium">
									<input
										{...props}
										type="checkbox"
										bind:checked={$formData.unit_rate_manual_override}
										onchange={handleManualOverrideChange}
									/>
									<span>Manual unit rate override</span>
								</label>
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div class="col-span-12 md:col-span-6">
					<Form.Field {form} name="subtotal">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Subtotal</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$formData.subtotal}
									readonly
									class="bg-muted"
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="col-span-12">
					<Form.Field {form} name="notes">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Notes</Form.Label>
								<Textarea {...props} bind:value={$formData.notes} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
			</div>

			<div class="space-y-2">
				<span class="text-sm font-medium">Normalization</span>
				<NormalizationInput
					bind:type={$formData.normalization_type}
					amount={$formData.normalization_amount?.toString() || ''}
					percentage={$formData.normalization_percentage?.toString() || ''}
					{subtotal}
					{currencySymbol}
					symbolPosition={currencySymbolPosition}
					onTypeChange={(type) => ($formData.normalization_type = type)}
					onAmountChange={(amount) => ($formData.normalization_amount = parseFloat(amount) || null)}
					onPercentageChange={(percentage) =>
						($formData.normalization_percentage = parseFloat(percentage) || null)}
				/>
			</div>

			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (open = false)}>Cancel</Button>
				<Button type="submit">{submitLabel}</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
