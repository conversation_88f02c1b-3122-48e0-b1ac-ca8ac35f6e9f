<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import WbsTreeSelector from './WbsTreeSelector.svelte';
	import CoverageInput from './CoverageInput.svelte';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';
	import type { WbsItemWithBudget, TenderWbsMappingRow } from '$lib/schemas/tender';
	import { formatCurrency } from '$lib/utils';
	import { buildAggregatedBudgetMap } from '$lib/wbs_utils';
	import { SvelteMap } from 'svelte/reactivity';
	import type { SuperForm } from 'sveltekit-superforms';

	import type {
		CreateWbsMappingForm,
		DeleteWbsMappingForm,
		EditWbsMappingForm,
		BulkWbsMappingForm,
		WbsMappingFormMessage,
	} from '$lib/types/wbs-mapping';

	interface Props {
		open: boolean;
		lineItemId: string;
		lineItemDescription: string;
		availableWbsItems: WbsItemWithBudget[];
		existingMappings: (TenderWbsMappingRow & {
			wbs_library_item: {
				code: string;
				description: string;
				level: number;
			};
		})[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
		createFormHandler: SuperForm<CreateWbsMappingForm, WbsMappingFormMessage>;
		editFormHandler: SuperForm<EditWbsMappingForm, WbsMappingFormMessage>;
		deleteFormHandler: SuperForm<DeleteWbsMappingForm, WbsMappingFormMessage>;
		bulkFormHandler: SuperForm<BulkWbsMappingForm, WbsMappingFormMessage>;
	}

	let {
		open = $bindable(false),
		lineItemId: _lineItemId,
		lineItemDescription,
		availableWbsItems,
		existingMappings = $bindable([]),
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
		createFormHandler,
		editFormHandler,
		deleteFormHandler,
		bulkFormHandler,
	}: Props = $props();

	let addDialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let mappingToDelete = $state<(typeof existingMappings)[0] | null>(null);
	let pendingDeleteForm: HTMLFormElement | null = null;
	let selectedWbsItems = $state<string[]>([]);
	let searchQuery = $state('');
	let selectedMapping = $state<(typeof existingMappings)[0] | null>(null);
	let multiSelectMode = $state(false);
	const createForm = createFormHandler.form;
	const enhanceCreate = createFormHandler.enhance;
	const createSubmitting = createFormHandler.submitting;
	const createMessage = createFormHandler.message;
	const editForm = editFormHandler.form;
	const enhanceEdit = editFormHandler.enhance;
	const editSubmitting = editFormHandler.submitting;
	const editMessage = editFormHandler.message;
	const enhanceDelete = deleteFormHandler.enhance;
	const deleteSubmitting = deleteFormHandler.submitting;
	const enhanceBulk = bulkFormHandler.enhance;
	const bulkSubmitting = bulkFormHandler.submitting;

	// Dynamic enhance function based on mode
	const dynamicEnhance = $derived(
		multiSelectMode && selectedWbsItems.length > 1 ? enhanceBulk : enhanceCreate,
	);

	// Dynamic submitting state based on mode
	const dynamicSubmitting = $derived(
		multiSelectMode && selectedWbsItems.length > 1 ? $bulkSubmitting : $createSubmitting,
	);
	const selectedEditWbsItem = $derived.by(() =>
		selectedMapping ? getWbsItem(selectedMapping.wbs_library_item_id) : undefined,
	);

	// Form state for new mapping
	let newMappingForm = $state({
		coverageType: 'percentage' as 'percentage' | 'quantity',
		coveragePercentage: 100,
		coverageQuantity: null as number | null,
		notes: '',
	});

	// Form state for editing mapping
	let editMappingForm = $state({
		coverageType: 'percentage' as 'percentage' | 'quantity',
		coveragePercentage: 100,
		coverageQuantity: null as number | null,
		notes: '',
	});

	$effect(() => {
		$createForm.notes = newMappingForm.notes ? newMappingForm.notes : null;
	});

	$effect(() => {
		$editForm.notes = editMappingForm.notes ? editMappingForm.notes : null;
	});

	$effect(() => {
		const message = $createMessage;
		if (message?.type !== 'success') return;

		addDialogOpen = false;
		selectedWbsItems = [];
		newMappingForm = {
			coverageType: 'percentage',
			coveragePercentage: 100,
			coverageQuantity: null,
			notes: '',
		};
		$createForm.wbs_library_item_id = '';
		$createForm.coverage_percentage = 100;
		$createForm.coverage_quantity = null;
		$createForm.notes = null;
	});

	$effect(() => {
		const message = $editMessage;
		if (message?.type !== 'success') return;

		open = false;
		selectedMapping = null;
		editMappingForm = {
			coverageType: 'percentage',
			coveragePercentage: 100,
			coverageQuantity: null,
			notes: '',
		};
		$editForm.coverage_percentage = undefined;
		$editForm.coverage_quantity = null;
		$editForm.notes = null;
	});

	// Calculate total coverage for validation
	const totalCoverage = $derived.by(() => {
		const coverageByWbs = new SvelteMap<string, number>();

		existingMappings.forEach((mapping) => {
			const current = coverageByWbs.get(mapping.wbs_library_item_id) || 0;
			coverageByWbs.set(mapping.wbs_library_item_id, current + (mapping.coverage_percentage || 0));
		});

		return coverageByWbs;
	});

	const aggregatedBudgets = $derived.by(() => buildAggregatedBudgetMap(availableWbsItems));

	// Get WBS item details by ID
	function getWbsItem(itemId: string): WbsItemWithBudget | undefined {
		return availableWbsItems.find((item) => item.wbs_library_item_id === itemId);
	}

	function getAggregatedBudgetAmount(itemId: string): number {
		return aggregatedBudgets.get(itemId)?.amount ?? 0;
	}

	function openAddDialog() {
		createFormHandler.reset();
		selectedWbsItems = [];
		newMappingForm = {
			coverageType: 'percentage',
			coveragePercentage: 100,
			coverageQuantity: null,
			notes: '',
		};
		$createForm.wbs_library_item_id = '';
		$createForm.coverage_percentage = newMappingForm.coveragePercentage;
		$createForm.coverage_quantity = null;
		$createForm.notes = null;
		addDialogOpen = true;
	}

	function openEditDialog(mapping: (typeof existingMappings)[0]) {
		editFormHandler.reset();
		selectedMapping = mapping;
		editMappingForm = {
			coverageType: mapping.coverage_quantity ? 'quantity' : 'percentage',
			coveragePercentage: mapping.coverage_percentage || 100,
			coverageQuantity: mapping.coverage_quantity || null,
			notes: mapping.notes || '',
		};
		$editForm.coverage_percentage = mapping.coverage_percentage ?? undefined;
		$editForm.coverage_quantity = mapping.coverage_quantity ?? null;
		$editForm.notes = mapping.notes ?? null;
		open = true;
	}

	function openDeleteDialog(mapping: (typeof existingMappings)[0]) {
		const form = document.getElementById(
			`delete-form-${mapping.tender_wbs_mapping_id}`,
		) as HTMLFormElement | null;
		pendingDeleteForm = form;
		mappingToDelete = mapping;
		deleteDialogOpen = true;
	}

	function closeDeleteDialog() {
		deleteDialogOpen = false;
		mappingToDelete = null;
		pendingDeleteForm = null;
	}

	function confirmDeleteMapping() {
		const form = pendingDeleteForm;
		closeDeleteDialog();
		if (!form) return;
		if (typeof form.requestSubmit === 'function') {
			form.requestSubmit();
		} else {
			form.submit();
		}
	}

	function handleWbsSelection(payload: { itemId: string; item: WbsItemWithBudget }) {
		const { itemId } = payload;
		if (!selectedWbsItems.includes(itemId)) {
			selectedWbsItems = [itemId]; // Single selection for now
		}
		$createForm.wbs_library_item_id = itemId;
		// Reset coverage defaults when a new item is chosen
		if (newMappingForm.coverageType === 'percentage') {
			$createForm.coverage_percentage = newMappingForm.coveragePercentage;
		} else {
			$createForm.coverage_quantity = newMappingForm.coverageQuantity;
		}
	}

	function handleWbsDeselection(payload: { itemId: string }) {
		const { itemId } = payload;
		selectedWbsItems = selectedWbsItems.filter((id) => id !== itemId);
		if (selectedWbsItems.length === 0) {
			$createForm.wbs_library_item_id = '';
		}
	}

	function handleBulkWbsSelection(payload: { itemIds: string[]; items: WbsItemWithBudget[] }) {
		const { itemIds } = payload;
		selectedWbsItems = [...new Set([...selectedWbsItems, ...itemIds])];
	}

	function handleCoverageChange(payload: {
		type: 'percentage' | 'quantity';
		percentage: number;
		quantity: number | null;
	}) {
		const { type, percentage, quantity } = payload;
		newMappingForm.coverageType = type;
		newMappingForm.coveragePercentage = percentage;
		newMappingForm.coverageQuantity = quantity;

		if (type === 'percentage') {
			$createForm.coverage_percentage = percentage;
			$createForm.coverage_quantity = null;
		} else {
			$createForm.coverage_quantity = quantity;
			$createForm.coverage_percentage = undefined;
		}
	}

	function handleEditCoverageChange(payload: {
		type: 'percentage' | 'quantity';
		percentage: number;
		quantity: number | null;
	}) {
		const { type, percentage, quantity } = payload;
		editMappingForm.coverageType = type;
		editMappingForm.coveragePercentage = percentage;
		editMappingForm.coverageQuantity = quantity;

		if (type === 'percentage') {
			$editForm.coverage_percentage = percentage;
			$editForm.coverage_quantity = null;
		} else {
			$editForm.coverage_quantity = quantity;
			$editForm.coverage_percentage = undefined;
		}
	}

	function getCoverageStatus(wbsItemId: string): 'complete' | 'partial' | 'over' {
		const coverage = totalCoverage.get(wbsItemId) || 0;
		if (coverage > 100) return 'over';
		if (coverage < 100) return 'partial';
		return 'complete';
	}

	function getCoverageVariant(status: 'complete' | 'partial' | 'over') {
		switch (status) {
			case 'complete':
				return 'default';
			case 'partial':
				return 'secondary';
			case 'over':
				return 'destructive';
		}
	}

	function calculateCoverageAmount(
		mapping: (typeof existingMappings)[0],
		wbsItem: WbsItemWithBudget | undefined = getWbsItem(mapping.wbs_library_item_id),
	): number {
		const budgetTotals = aggregatedBudgets.get(mapping.wbs_library_item_id);
		const budgetAmount = budgetTotals?.amount ?? wbsItem?.budget_amount ?? 0;
		if (mapping.coverage_quantity != null) {
			const budgetQuantity = budgetTotals?.quantity ?? wbsItem?.budget_quantity ?? 0;
			if (!budgetAmount || !budgetQuantity) return 0;
			return (budgetAmount * mapping.coverage_quantity) / budgetQuantity;
		}

		const coveragePercentage = mapping.coverage_percentage ?? 0;
		if (!budgetAmount || !coveragePercentage) return 0;

		return (budgetAmount * coveragePercentage) / 100;
	}

	const totalCoverageAmount = $derived.by(() =>
		existingMappings.reduce((sum, mapping) => sum + calculateCoverageAmount(mapping), 0),
	);
</script>

<div class="space-y-4">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h3 class="text-lg font-semibold">WBS Mapping</h3>
			<p class="text-sm text-gray-600">Map this line item to WBS codes</p>
		</div>
		<Button onclick={openAddDialog} {disabled}>
			<PlusIcon class="mr-2 h-4 w-4" />
			Add Mapping
		</Button>
	</div>

	<!-- Existing Mappings -->
	{#if existingMappings.length > 0}
		<div class="overflow-x-auto rounded-lg border">
			<Table class="min-w-[720px]">
				<TableHeader>
					<TableRow>
						<TableHead class="w-[120px]">Code</TableHead>
						<TableHead>Description</TableHead>
						<TableHead class="text-right">Budget</TableHead>
						<TableHead class="text-right">Mapped Coverage (%)</TableHead>
						<TableHead class="text-right">Coverage Amount</TableHead>
						<TableHead class="w-[120px] text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each existingMappings as mapping (mapping.tender_wbs_mapping_id)}
						{@const wbsItem = getWbsItem(mapping.wbs_library_item_id)}
						{@const coverageStatus = getCoverageStatus(mapping.wbs_library_item_id)}
						{@const coverageAmount = calculateCoverageAmount(mapping, wbsItem)}

						<TableRow>
							<TableCell class="font-mono text-sm font-medium text-gray-900">
								{wbsItem?.code || 'Unknown'}
							</TableCell>
							<TableCell>
								<div class="text-sm text-gray-900">
									{wbsItem?.description || 'Unknown WBS item'}
								</div>
								{#if mapping.notes}
									<div class="mt-1 text-xs text-gray-500">Notes: {mapping.notes}</div>
								{/if}
							</TableCell>
							<TableCell class="text-right text-sm font-medium">
								{formatCurrency(getAggregatedBudgetAmount(mapping.wbs_library_item_id), {
									symbol: currencySymbol,
									symbolPosition,
									fallback: '-',
									maximumFractionDigits: 0,
								})}
							</TableCell>
							<TableCell class="text-right">
								<div class="flex justify-end">
									<Badge variant={getCoverageVariant(coverageStatus)}>
										{mapping.coverage_percentage ?? 0}%
									</Badge>
								</div>
							</TableCell>
							<TableCell class="text-right text-sm">
								{#if mapping.coverage_quantity != null}
									<div class="flex justify-end gap-1">
										<span>{mapping.coverage_quantity}</span>
										{#if wbsItem?.unit}
											<span>{wbsItem.unit}</span>
										{/if}
									</div>
								{:else}
									<span class="text-right text-sm"
										>{formatCurrency(coverageAmount, {
											symbol: currencySymbol,
											symbolPosition,
											fallback: '-',
											maximumFractionDigits: 0,
										})}</span
									>
								{/if}
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0" {disabled}>
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<DropdownMenu.Item onclick={() => openEditDialog(mapping)} {disabled}>
											<PencilIcon class="mr-2 h-4 w-4" />
											Edit
										</DropdownMenu.Item>
										<DropdownMenu.Item
											onclick={() => openDeleteDialog(mapping)}
											disabled={disabled || $deleteSubmitting}
											variant="destructive"
										>
											<TrashIcon class="mr-2 h-4 w-4" />
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
								<form
									id={`delete-form-${mapping.tender_wbs_mapping_id}`}
									method="POST"
									action="?/deleteMapping"
									use:enhanceDelete
									style="display: contents"
								>
									<input
										type="hidden"
										name="tender_wbs_mapping_id"
										value={mapping.tender_wbs_mapping_id}
									/>
								</form>
							</TableCell>
						</TableRow>
					{/each}
					<TableRow class="bg-gray-50">
						<TableCell colspan={4} class="text-left text-sm font-semibold text-gray-900">
							Total
						</TableCell>
						<TableCell class="text-right text-sm font-semibold">
							{formatCurrency(totalCoverageAmount, {
								symbol: currencySymbol,
								symbolPosition,
								fallback: '-',
								maximumFractionDigits: 0,
							})}
						</TableCell>
						<TableCell></TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="py-8 text-center text-gray-500">
			<p class="text-sm">No WBS mappings yet</p>
			<p class="text-xs">Add mappings to connect this line item to your project's WBS structure</p>
		</div>
	{/if}
</div>

<!-- Add Mapping Dialog -->
<Dialog.Root bind:open={addDialogOpen}>
	<Dialog.Content class="max-h-[90vh] overflow-y-auto lg:max-w-[90vw]">
		<Dialog.Header>
			<Dialog.Title>Add WBS Mapping</Dialog.Title>
			<Dialog.Description>
				Map "{lineItemDescription}" to WBS items
			</Dialog.Description>
		</Dialog.Header>

		<form
			method="POST"
			action={multiSelectMode && selectedWbsItems.length > 1
				? '?/bulkCreateMappings'
				: '?/createMapping'}
			use:dynamicEnhance
			class="grid grid-cols-1 gap-6 py-4 lg:grid-cols-2"
		>
			<input
				type="hidden"
				name="wbs_library_item_id"
				value={$createForm.wbs_library_item_id ?? ''}
			/>
			<input
				type="hidden"
				name="coverage_percentage"
				value={$createForm.coverage_percentage ?? ''}
				disabled={newMappingForm.coverageType !== 'percentage'}
			/>
			<input
				type="hidden"
				name="coverage_quantity"
				value={$createForm.coverage_quantity ?? ''}
				disabled={newMappingForm.coverageType !== 'quantity'}
			/>

			<!-- Bulk mapping data (when in multi-select mode) -->
			{#if multiSelectMode && selectedWbsItems.length > 1}
				{#each selectedWbsItems as itemId, index (itemId)}
					<input type="hidden" name="mappings[{index}].wbs_library_item_id" value={itemId} />
					<input
						type="hidden"
						name="mappings[{index}].coverage_percentage"
						value={newMappingForm.coverageType === 'percentage'
							? newMappingForm.coveragePercentage
							: ''}
					/>
					<input
						type="hidden"
						name="mappings[{index}].coverage_quantity"
						value={newMappingForm.coverageType === 'quantity'
							? (newMappingForm.coverageQuantity ?? '')
							: ''}
					/>
					<input type="hidden" name="mappings[{index}].notes" value={newMappingForm.notes || ''} />
				{/each}
			{/if}

			<!-- WBS Selection -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<Label class="text-sm font-medium">Select WBS Items</Label>
					<!-- TODO: Figure out a way to do multi-select that makes sense -->
					<!-- <div class="flex items-center space-x-2">
						<input
							type="checkbox"
							id="multi-select-mode"
							bind:checked={multiSelectMode}
							class="rounded border-gray-300"
						/>
						<Label for="multi-select-mode" class="text-sm">Multi-select mode</Label>
					</div> -->
				</div>
				<WbsTreeSelector
					wbsItems={availableWbsItems}
					bind:selectedItems={selectedWbsItems}
					bind:searchQuery
					allowMultiSelect={multiSelectMode}
					{currencySymbol}
					{symbolPosition}
					onSelect={handleWbsSelection}
					onDeselect={handleWbsDeselection}
					onBulkSelect={handleBulkWbsSelection}
				/>
			</div>

			<!-- Coverage Configuration -->
			<div class="space-y-4">
				{#if selectedWbsItems.length > 0}
					{#if multiSelectMode && selectedWbsItems.length > 1}
						<!-- Multi-select mode: Show selected items list -->
						<div>
							<Label class="text-sm font-medium"
								>Selected WBS Items ({selectedWbsItems.length})</Label
							>
							<div class="mt-2 max-h-32 space-y-2 overflow-y-auto">
								{#each selectedWbsItems as itemId (itemId)}
									{@const item = getWbsItem(itemId)}
									{#if item}
										<div class="flex items-center justify-between rounded-md bg-gray-50 p-2">
											<div class="min-w-0 flex-1">
												<div class="text-sm font-medium">{item.code}</div>
												<div class="truncate text-xs text-gray-600">{item.description}</div>
											</div>
											<Button
												variant="ghost"
												size="sm"
												onclick={() => handleWbsDeselection({ itemId })}
												class="h-auto px-2 py-1"
											>
												×
											</Button>
										</div>
									{/if}
								{/each}
							</div>
						</div>

						<!-- Bulk coverage configuration -->
						<div>
							<Label class="text-sm font-medium">Coverage Configuration (Applied to All)</Label>
							<CoverageInput
								bind:coverageType={newMappingForm.coverageType}
								bind:coveragePercentage={newMappingForm.coveragePercentage}
								bind:coverageQuantity={newMappingForm.coverageQuantity}
								{currencySymbol}
								{symbolPosition}
								onChange={handleCoverageChange}
								{disabled}
								showCalculatedAmount={false}
							/>
						</div>
					{:else}
						<!-- Single-select mode: Show single item -->
						{@const selectedWbsItem = getWbsItem(selectedWbsItems[0])}
						{@const selectedBudgetTotals = selectedWbsItem
							? aggregatedBudgets.get(selectedWbsItem.wbs_library_item_id)
							: null}
						<div>
							<Label class="text-sm font-medium">Coverage Configuration</Label>
							<div class="mt-2 rounded-md bg-gray-50 p-3">
								<div class="text-sm font-medium">{selectedWbsItem?.code}</div>
								<div class="text-xs text-gray-600">{selectedWbsItem?.description}</div>
							</div>
						</div>

						<CoverageInput
							bind:coverageType={newMappingForm.coverageType}
							bind:coveragePercentage={newMappingForm.coveragePercentage}
							bind:coverageQuantity={newMappingForm.coverageQuantity}
							budgetQuantity={selectedBudgetTotals?.quantity}
							budgetAmount={selectedBudgetTotals?.amount}
							unit={selectedWbsItem?.unit}
							{currencySymbol}
							{symbolPosition}
							onChange={handleCoverageChange}
							{disabled}
						/>
					{/if}

					<div class="space-y-2">
						<Label for="mapping-notes" class="text-sm font-medium">Notes (Optional)</Label>
						<Textarea
							id="mapping-notes"
							name="notes"
							placeholder="Add notes about this mapping..."
							bind:value={newMappingForm.notes}
							rows={3}
							{disabled}
						/>
					</div>
				{:else}
					<div class="py-8 text-center text-gray-500">
						<p class="text-sm">Select a WBS item to configure coverage</p>
					</div>
				{/if}
			</div>

			<Dialog.Footer class="lg:col-span-2">
				<Button
					type="button"
					variant="outline"
					onclick={() => {
						addDialogOpen = false;
						createFormHandler.reset();
						selectedWbsItems = [];
					}}
				>
					Cancel
				</Button>
				<Button
					type="submit"
					disabled={disabled || selectedWbsItems.length === 0 || dynamicSubmitting}
					onclick={() => {
						addDialogOpen = false;
					}}
				>
					{multiSelectMode && selectedWbsItems.length > 1
						? `Add ${selectedWbsItems.length} Mappings`
						: 'Add Mapping'}
				</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>

<!-- Edit Mapping Dialog -->
<Dialog.Root bind:open>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>Edit WBS Mapping</Dialog.Title>
			<Dialog.Description>
				Update coverage for {selectedMapping?.wbs_library_item.code}
			</Dialog.Description>
		</Dialog.Header>

		{#if selectedMapping}
			{@const editBudgetTotals = aggregatedBudgets.get(selectedMapping.wbs_library_item_id)}
			<form method="POST" action="?/updateMapping" use:enhanceEdit class="space-y-4 py-4">
				<input
					type="hidden"
					name="tender_wbs_mapping_id"
					value={selectedMapping.tender_wbs_mapping_id}
				/>
				<input
					type="hidden"
					name="coverage_percentage"
					value={$editForm.coverage_percentage ?? ''}
					disabled={editMappingForm.coverageType !== 'percentage'}
				/>
				<input
					type="hidden"
					name="coverage_quantity"
					value={$editForm.coverage_quantity ?? ''}
					disabled={editMappingForm.coverageType !== 'quantity'}
				/>

				<div class="rounded-md bg-gray-50 p-3">
					<div class="text-sm font-medium">{selectedMapping.wbs_library_item.code}</div>
					<div class="text-xs text-gray-600">{selectedMapping.wbs_library_item.description}</div>
				</div>

				<CoverageInput
					bind:coverageType={editMappingForm.coverageType}
					bind:coveragePercentage={editMappingForm.coveragePercentage}
					bind:coverageQuantity={editMappingForm.coverageQuantity}
					budgetQuantity={editBudgetTotals?.quantity}
					budgetAmount={editBudgetTotals?.amount}
					unit={selectedEditWbsItem?.unit}
					{currencySymbol}
					{symbolPosition}
					onChange={handleEditCoverageChange}
					{disabled}
				/>

				<div class="space-y-2">
					<Label for="edit-mapping-notes" class="text-sm font-medium">Notes (Optional)</Label>
					<Textarea
						id="edit-mapping-notes"
						name="notes"
						placeholder="Add notes about this mapping..."
						bind:value={editMappingForm.notes}
						rows={3}
						{disabled}
					/>
				</div>

				<Dialog.Footer class="pt-2">
					<Button
						type="button"
						variant="outline"
						onclick={() => {
							open = false;
							selectedMapping = null;
							editFormHandler.reset();
						}}
					>
						Cancel
					</Button>
					<Button type="submit" disabled={disabled || $editSubmitting}>Update Mapping</Button>
				</Dialog.Footer>
			</form>
		{/if}
	</Dialog.Content>
</Dialog.Root>

<!-- Delete Mapping Confirmation Dialog -->
<Dialog.Root bind:open={deleteDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Delete WBS Mapping</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to delete this mapping? This action cannot be undone.
			</Dialog.Description>
		</Dialog.Header>

		{#if mappingToDelete}
			<div class="py-4 text-sm text-gray-600">
				<p class="font-medium text-gray-900">
					{mappingToDelete.wbs_library_item.code} - {mappingToDelete.wbs_library_item.description}
				</p>
				<p class="mt-1">
					Coverage: {mappingToDelete.coverage_percentage ?? 0}%
				</p>
			</div>
		{/if}

		<Dialog.Footer>
			<Button type="button" variant="outline" onclick={closeDeleteDialog}>Cancel</Button>
			<Button type="button" variant="destructive" onclick={confirmDeleteMapping}>Delete</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
