<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import * as Chart from '$lib/components/ui/chart/index.js';
	import { formatCurrency } from '$lib/utils';
	import { SvelteMap } from 'svelte/reactivity';
	import { Chart as Layer<PERSON><PERSON>, Layer, Sankey, Link, Group, Rect, Text } from 'layerchart';
	import type { BudgetTransfer } from './budget-transfer.types';

	interface Props {
		existingTransfers?: BudgetTransfer[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		title?: string;
		description?: string;
	}

	let {
		existingTransfers = [],
		currencySymbol = 'kr',
		symbolPosition = 'after',
		title = 'WBS Budget Transfers',
		description,
	}: Props = $props();

	const sankeyPalette = [
		'hsl(var(--aquamarine-500))',
		'hsl(var(--aquamarine-300))',
		'hsl(var(--aquamarine-700))',
		'hsl(var(--aquamarine-200))',
		'hsl(var(--muted-foreground))',
		'hsl(var(--primary))',
	];

	type SankeyNode = {
		id: string;
		baseId: string;
		code: string;
		description: string;
		label: string;
		role: 'start' | 'end';
	};

	type SankeyLink = {
		source: string;
		target: string;
		value: number;
	};

	const sankeyData = $derived.by(() => {
		if (!existingTransfers.length) return null;

		const nodes = new SvelteMap<string, SankeyNode>();
		const links = new SvelteMap<string, SankeyLink>();

		existingTransfers.forEach((transfer) => {
			const amount = transfer.transfer_amount || 0;
			if (amount <= 0) return;

			const sourceBaseId = transfer.from_wbs_library_item_id;
			const targetBaseId = transfer.to_wbs_library_item_id;
			if (!sourceBaseId || !targetBaseId) return;

			const sourceNodeId = `start:${sourceBaseId}`;
			const targetNodeId = `end:${targetBaseId}`;
			const fromLabel = transfer.from_wbs_item?.code || 'Unknown';
			const toLabel = transfer.to_wbs_item?.code || 'Unknown';

			nodes.set(sourceNodeId, {
				id: sourceNodeId,
				baseId: sourceBaseId,
				code: fromLabel,
				description: transfer.from_wbs_item?.description || '',
				label: fromLabel,
				role: 'start',
			});
			nodes.set(targetNodeId, {
				id: targetNodeId,
				baseId: targetBaseId,
				code: toLabel,
				description: transfer.to_wbs_item?.description || '',
				label: toLabel,
				role: 'end',
			});

			const linkKey = `${sourceNodeId}->${targetNodeId}`;
			const current = links.get(linkKey);
			links.set(linkKey, {
				source: sourceNodeId,
				target: targetNodeId,
				value: (current?.value || 0) + amount,
			});
		});

		const linkValues = Array.from(links.values()).filter((link) => link.value > 0);
		if (nodes.size === 0 || linkValues.length === 0) {
			return null;
		}

		return {
			nodes: Array.from(nodes.values()),
			links: linkValues,
		};
	});

	type SankeyConfig = Chart.ChartConfig;

	const sankeyChartConfig = $derived.by(() => {
		const data = sankeyData;
		if (!data) {
			return {
				flow: {
					label: 'Budget Transfers',
					color: 'var(--chart-1)',
				},
			} satisfies SankeyConfig;
		}

		const config: SankeyConfig = {};
		const colorAssignments = new SvelteMap<string, string>();
		let paletteIndex = 0;

		data.nodes.forEach((node) => {
			let color = colorAssignments.get(node.baseId);
			if (!color) {
				color = sankeyPalette[paletteIndex % sankeyPalette.length];
				colorAssignments.set(node.baseId, color);
				paletteIndex += 1;
			}

			config[node.id] = {
				label: node.label,
				color,
			};
		});

		return config;
	});

	const totalSankeyValue = $derived.by(() => {
		const data = sankeyData;
		if (!data) return 0;
		return data.links.reduce((sum, link) => sum + link.value, 0);
	});

	function isFiniteNumber(value: number | null | undefined): value is number {
		return typeof value === 'number' && Number.isFinite(value);
	}
</script>

<Card.Root>
	<Card.Header>
		<Card.Title class="text-base">{title}</Card.Title>
		<Card.Description>
			{#if description}
				{description}
			{:else if totalSankeyValue > 0}
				Visualizes {existingTransfers.length} budget transfers ({formatCurrency(totalSankeyValue, {
					symbol: currencySymbol,
					symbolPosition,
					fallback: '-',
				})} total)
			{:else}
				Add budget transfers to visualize WBS relationships
			{/if}
		</Card.Description>
	</Card.Header>
	<Card.Content>
		{#if sankeyData}
			<div class="overflow-x-auto">
				<Chart.Container config={sankeyChartConfig} class="h-[360px] w-full sm:h-[420px]">
					<LayerChart data={sankeyData} flatData={[]}>
						<Layer type="svg" class="text-muted-foreground">
							<Sankey nodeId={(node) => node.id} nodePadding={32} nodeWidth={16}>
								{#snippet children({ links, nodes })}
									<g class="fill-none">
										{#each links as link (`${link.source.id}-${link.target.id}`)}
											{@const linkColor =
												sankeyChartConfig[link.source.id]?.color ?? sankeyPalette[0]}
											{@const formattedValue = formatCurrency(link.value, {
												symbol: currencySymbol,
												symbolPosition,
												fallback: '-',
											})}
											{@const strokeWidth = Math.max(4, (link.width ?? 0) / 2)}
											<Link
												sankey
												data={link}
												{strokeWidth}
												class="opacity-80 transition-opacity hover:opacity-100"
												stroke={linkColor}
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-opacity={0.6}
												fill="none"
											>
												<title>
													{sankeyChartConfig[link.source.id]?.label || link.source.id}
													→
													{sankeyChartConfig[link.target.id]?.label || link.target.id}
													• {formattedValue}
												</title>
											</Link>
										{/each}
									</g>
									{#each nodes as node (node.id)}
										{@const x0 = node.x0}
										{@const x1 = node.x1}
										{@const y0 = node.y0}
										{@const y1 = node.y1}
										{@const hasBounds =
											isFiniteNumber(x0) &&
											isFiniteNumber(x1) &&
											isFiniteNumber(y0) &&
											isFiniteNumber(y1)}
										{#if hasBounds}
											{@const nodeStartX = x0 as number}
											{@const nodeEndX = x1 as number}
											{@const nodeStartY = y0 as number}
											{@const nodeEndY = y1 as number}
											{@const nodeWidth = Math.max(12, nodeEndX - nodeStartX)}
											{@const nodeHeight = Math.max(4, nodeEndY - nodeStartY)}
											{@const nodeColor = sankeyChartConfig[node.id]?.color ?? sankeyPalette[0]}
											{@const textX = node.role === 'end' ? -8 : nodeWidth + 8}
											{@const textAnchor = node.role === 'end' ? 'end' : 'start'}
											<Group x={nodeStartX} y={nodeStartY} class="text-xs">
												<Rect
													width={nodeWidth}
													height={nodeHeight}
													rx={6}
													ry={6}
													fill={nodeColor}
													opacity={0.9}
													stroke="hsl(var(--border))"
													strokeWidth={1}
												>
													<title>
														{node.label}
														{#if node.description}
															— {node.description}
														{/if}
													</title>
												</Rect>
												<Text
													value={node.label}
													x={textX}
													y={nodeHeight / 2}
													{textAnchor}
													verticalAnchor="middle"
													class="fill-foreground font-medium"
												/>
											</Group>
										{/if}
									{/each}
								{/snippet}
							</Sankey>
						</Layer>
					</LayerChart>
				</Chart.Container>
			</div>
		{:else}
			<p class="text-muted-foreground text-sm">
				No budget transfers to visualize yet. Create a transfer to see the WBS flow.
			</p>
		{/if}
	</Card.Content>
</Card.Root>
