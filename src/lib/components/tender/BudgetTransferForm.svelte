<script lang="ts">
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import * as Form from '$lib/components/ui/form';
	import { Badge } from '$lib/components/ui/badge';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import ArrowsSplitIcon from 'phosphor-svelte/lib/ArrowsSplit';
	import { budgetTransferSchema, type WbsItemWithBudget } from '$lib/schemas/tender';
	import { cn, formatCurrency } from '$lib/utils';
	import { useId } from 'bits-ui';
	import { tick } from 'svelte';
	import { SvelteMap } from 'svelte/reactivity';
	import { superForm, type Infer, type SuperValidated } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { toast } from 'svelte-sonner';
	import { invalidateAll } from '$app/navigation';
	import type { BudgetTransfer } from './budget-transfer.types';

	interface Props {
		availableWbsItems: WbsItemWithBudget[];
		existingTransfers?: BudgetTransfer[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
		transferForm: SuperValidated<Infer<typeof budgetTransferSchema>>;
		showCreateButton?: boolean;
		dialogTitle?: string;
		dialogDescription?: string;
	}

	let {
		availableWbsItems,
		existingTransfers = [],
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
		transferForm: transferFormData,
		showCreateButton = true,
		dialogTitle = 'Create Budget Transfer',
		dialogDescription = 'Transfer budget amount between WBS items',
	}: Props = $props();

	const createTransferForm = superForm(transferFormData, {
		id: 'tender-budget-transfer-form',
		validators: zodClient(budgetTransferSchema),
		resetForm: true,
		onUpdated: async ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					await invalidateAll();
					closeCreateDialog();
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	const { form: formState, enhance, submitting } = createTransferForm;

	let createDialogOpen = $state(false);
	let fromWbsOpen = $state(false);
	let toWbsOpen = $state(false);

	const fromTriggerId = useId();
	const toTriggerId = useId();

	function openCreateDialog() {
		if (disabled) return;
		$formState.from_wbs_library_item_id = '';
		$formState.to_wbs_library_item_id = '';
		$formState.transfer_amount = 0;
		$formState.transfer_reason = '';
		createDialogOpen = true;
	}

	function closeCreateDialog() {
		createDialogOpen = false;
	}

	function closeAndFocusTrigger(triggerId: string, setOpen: (value: boolean) => void) {
		setOpen(false);
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}

	const wbsBalances = $derived.by(() => {
		const balances = new SvelteMap<string, number>();

		availableWbsItems.forEach((item) => {
			if (item.budget_amount) {
				balances.set(item.wbs_library_item_id, item.budget_amount);
			}
		});

		existingTransfers.forEach((transfer) => {
			const fromBalance = balances.get(transfer.from_wbs_library_item_id) || 0;
			const toBalance = balances.get(transfer.to_wbs_library_item_id) || 0;

			balances.set(transfer.from_wbs_library_item_id, fromBalance - transfer.transfer_amount);
			balances.set(transfer.to_wbs_library_item_id, toBalance + transfer.transfer_amount);
		});

		return balances;
	});

	function getAvailableBalance(itemId: string): number {
		return wbsBalances.get(itemId) || 0;
	}

	const wbsOptions = $derived.by(() =>
		availableWbsItems.map((item) => ({
			value: item.wbs_library_item_id,
			label: `${item.code}: ${item.description}`,
		})),
	);
</script>

{#if showCreateButton}
	<div class="flex items-center justify-end">
		<Button onclick={openCreateDialog} {disabled}>
			<ArrowsSplitIcon class="mr-2 h-4 w-4 rotate-270" />
			Create Transfer
		</Button>
	</div>
{/if}

<Dialog.Root bind:open={createDialogOpen}>
	<Dialog.Content class="max-w-2xl">
		<Dialog.Header>
			<Dialog.Title>{dialogTitle}</Dialog.Title>
			<Dialog.Description>{dialogDescription}</Dialog.Description>
		</Dialog.Header>

		<form method="POST" action="?/createTransfer" use:enhance class="space-y-6 py-4">
			<div class="space-y-4">
				<Form.Field form={createTransferForm} name="from_wbs_library_item_id">
					<Popover.Root bind:open={fromWbsOpen}>
						<Form.Control id={fromTriggerId}>
							{#snippet children({ props })}
								<Form.Label>From WBS Item</Form.Label>
								<Popover.Trigger
									{...props}
									class={cn(
										buttonVariants({ variant: 'outline' }),
										'w-full justify-between',
										!$formState.from_wbs_library_item_id && 'text-muted-foreground',
									)}
									role="combobox"
									aria-expanded={fromWbsOpen}
									disabled={disabled || $submitting}
								>
									<span class="truncate">
										{$formState.from_wbs_library_item_id
											? wbsOptions.find((opt) => opt.value === $formState.from_wbs_library_item_id)
													?.label
											: 'Select source WBS item'}
									</span>
									<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
								</Popover.Trigger>
								<input hidden name={props.name} value={$formState.from_wbs_library_item_id ?? ''} />
							{/snippet}
						</Form.Control>
						<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
							<Command.Root>
								<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
								<Command.Empty>No matching items found.</Command.Empty>
								<Command.Group class="max-h-60 overflow-y-auto">
									{#each wbsOptions as option (option.value)}
										{@const balance = getAvailableBalance(option.value)}
										<Command.Item
											class="w-full items-center gap-2"
											value={option.label}
											disabled={disabled || $submitting || balance <= 0}
											onSelect={() => {
												if (disabled || $submitting) return;
												$formState.from_wbs_library_item_id = option.value;
												closeAndFocusTrigger(fromTriggerId, (value) => (fromWbsOpen = value));
											}}
										>
											<div class="flex min-w-0 flex-1 items-center justify-between gap-2">
												<span class="truncate">{option.label}</span>
												<Badge variant={balance > 0 ? 'default' : 'secondary'} class="shrink-0">
													{formatCurrency(balance, {
														symbol: currencySymbol,
														symbolPosition,
														fallback: '0',
													})}
												</Badge>
											</div>
											<CheckIcon
												class={cn(
													'ml-2 size-4 shrink-0',
													option.value !== $formState.from_wbs_library_item_id &&
														'text-transparent',
												)}
											/>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					{#if $formState.from_wbs_library_item_id}
						{@const availableAmount = getAvailableBalance($formState.from_wbs_library_item_id)}
						<div class="text-xs text-gray-500">
							Available: {formatCurrency(availableAmount, {
								symbol: currencySymbol,
								symbolPosition,
								fallback: '0',
							})}
						</div>
					{/if}
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={createTransferForm} name="to_wbs_library_item_id">
					<Popover.Root bind:open={toWbsOpen}>
						<Form.Control id={toTriggerId}>
							{#snippet children({ props })}
								<Form.Label>To WBS Item</Form.Label>
								<Popover.Trigger
									{...props}
									class={cn(
										buttonVariants({ variant: 'outline' }),
										'w-full justify-between',
										!$formState.to_wbs_library_item_id && 'text-muted-foreground',
									)}
									role="combobox"
									aria-expanded={toWbsOpen}
									disabled={disabled || $submitting}
								>
									<span class="truncate">
										{$formState.to_wbs_library_item_id
											? wbsOptions.find((opt) => opt.value === $formState.to_wbs_library_item_id)
													?.label
											: 'Select target WBS item'}
									</span>
									<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
								</Popover.Trigger>
								<input hidden name={props.name} value={$formState.to_wbs_library_item_id ?? ''} />
							{/snippet}
						</Form.Control>
						<Popover.Content class="w-[--radix-popover-trigger-width] p-0" align="start">
							<Command.Root>
								<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
								<Command.Empty>No matching items found.</Command.Empty>
								<Command.Group class="max-h-60 overflow-y-auto">
									{#each wbsOptions as option (option.value)}
										<Command.Item
											class="w-full items-center gap-2"
											value={option.label}
											disabled={disabled ||
												$submitting ||
												option.value === $formState.from_wbs_library_item_id}
											onSelect={() => {
												if (disabled || $submitting) return;
												$formState.to_wbs_library_item_id = option.value;
												closeAndFocusTrigger(toTriggerId, (value) => (toWbsOpen = value));
											}}
										>
											<span class="truncate">{option.label}</span>
											<CheckIcon
												class={cn(
													'ml-auto size-4 shrink-0',
													option.value !== $formState.to_wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
									{/each}
								</Command.Group>
							</Command.Root>
						</Popover.Content>
					</Popover.Root>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={createTransferForm} name="transfer_amount">
					<Form.Control>
						{#snippet children({ props })}
							<div class="flex items-center justify-between">
								<Form.Label>Transfer Amount</Form.Label>
							</div>
							<div class="flex items-center space-x-2">
								<Input
									{...props}
									type="number"
									min="0"
									step="0.01"
									class="flex-1"
									bind:value={$formState.transfer_amount}
									disabled={disabled || $submitting}
									placeholder="0.00"
								/>
								<span class="text-sm text-gray-500">{currencySymbol}</span>
							</div>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field form={createTransferForm} name="transfer_reason">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Reason</Form.Label>
							<Textarea
								{...props}
								rows={3}
								placeholder="Explain why this transfer is needed..."
								bind:value={$formState.transfer_reason}
								disabled={disabled || $submitting}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>
			</div>

			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={closeCreateDialog}>Cancel</Button>
				<Button type="submit" disabled={disabled || $submitting}>Create Transfer</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
