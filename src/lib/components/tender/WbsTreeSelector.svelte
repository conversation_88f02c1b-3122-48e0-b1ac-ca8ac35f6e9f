<script lang="ts">
	import CaretDownIcon from 'phosphor-svelte/lib/CaretDown';
	import CaretRightIcon from 'phosphor-svelte/lib/CaretRight';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Badge } from '$lib/components/ui/badge';
	import type { WbsItemWithBudget } from '$lib/schemas/tender';
	import { formatCurrency } from '$lib/utils';
	import { wbsCodeComparator } from '$lib/budget_utils';
	import { buildAggregatedBudgetMap } from '$lib/wbs_utils';
	import { SvelteMap } from 'svelte/reactivity';

	type WbsTreeNode = WbsItemWithBudget & { children: WbsTreeNode[] };

	interface Props {
		wbsItems: WbsItemWithBudget[];
		selectedItems: string[];
		searchQuery?: string;
		showBudgetInfo?: boolean;
		allowMultiSelect?: boolean;
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		onSelect?: (payload: { itemId: string; item: WbsItemWithBudget }) => void;
		onDeselect?: (payload: { itemId: string }) => void;
		onBulkSelect?: (payload: { itemIds: string[]; items: WbsItemWithBudget[] }) => void;
	}

	let {
		wbsItems,
		selectedItems = $bindable([]),
		searchQuery = $bindable(''),
		showBudgetInfo = true,
		allowMultiSelect = true,
		currencySymbol = 'kr',
		symbolPosition = 'after',
		onSelect = () => {},
		onDeselect = () => {},
		onBulkSelect = () => {},
	}: Props = $props();

	let expandedCategories = $state<Record<string, boolean>>({});

	const aggregatedBudgets = $derived.by(() => buildAggregatedBudgetMap(wbsItems));

	// Build hierarchical tree structure
	const wbsTree = $derived.by(() => {
		const itemMap = new SvelteMap<string, WbsTreeNode>();
		const codeMap = new SvelteMap<string, WbsTreeNode>();
		const rootItems: WbsTreeNode[] = [];

		const codeKey = (libraryId: string | null, code: string) => `${libraryId ?? 'global'}::${code}`;

		// Create all nodes with empty children arrays and index them by id/code for quick lookup
		wbsItems.forEach((item) => {
			const node: WbsTreeNode = { ...item, children: [] };
			itemMap.set(item.wbs_library_item_id, node);
			codeMap.set(codeKey(item.wbs_library_id ?? null, item.code), node);
		});

		const sortChildren = (nodes: WbsTreeNode[]) => {
			nodes.sort((a, b) => wbsCodeComparator(a.code, b.code));
			nodes.forEach((child) => {
				if (child.children.length > 0) {
					sortChildren(child.children);
				}
			});
		};

		// Build parent-child relationships, falling back to code-based lookup when parent ID is missing
		wbsItems.forEach((item) => {
			const node = itemMap.get(item.wbs_library_item_id);
			if (!node) return;

			if (!item.parent_item_id) {
				rootItems.push(node);
				return;
			}

			let parent = itemMap.get(item.parent_item_id);

			if (!parent) {
				const parentCode = item.code.includes('.')
					? item.code.slice(0, item.code.lastIndexOf('.'))
					: null;

				if (parentCode) {
					parent = codeMap.get(codeKey(item.wbs_library_id ?? null, parentCode));
				}
			}

			if (parent && parent !== node) {
				parent.children.push(node);
			} else {
				rootItems.push(node);
			}
		});

		sortChildren(rootItems);

		return rootItems;
	});

	// Filter tree based on search query
	const filteredTree = $derived.by(() => {
		if (!searchQuery.trim()) return wbsTree;

		const query = searchQuery.toLowerCase();
		const matchesSearch = (item: WbsItemWithBudget): boolean => {
			return (
				item.code.toLowerCase().includes(query) || item.description.toLowerCase().includes(query)
			);
		};

		const filterNode = (node: WbsTreeNode): WbsTreeNode | null => {
			const filteredChildren = node.children.map(filterNode).filter(Boolean) as WbsTreeNode[];

			if (matchesSearch(node) || filteredChildren.length > 0) {
				return { ...node, children: filteredChildren };
			}

			return null;
		};

		return wbsTree.map(filterNode).filter(Boolean) as WbsTreeNode[];
	});

	function toggleCategory(itemId: string) {
		expandedCategories[itemId] = !expandedCategories[itemId];
	}

	function handleItemSelect(item: WbsItemWithBudget, checked: boolean) {
		if (checked) {
			if (allowMultiSelect) {
				selectedItems = [...selectedItems, item.wbs_library_item_id];
			} else {
				selectedItems = [item.wbs_library_item_id];
			}
			onSelect({ itemId: item.wbs_library_item_id, item });
		} else {
			selectedItems = selectedItems.filter((id) => id !== item.wbs_library_item_id);
			onDeselect({ itemId: item.wbs_library_item_id });
		}
	}

	function selectAllChildren(node: WbsTreeNode) {
		const childIds: string[] = [];
		const childItems: WbsItemWithBudget[] = [];

		const collectChildren = (n: WbsTreeNode) => {
			if (n.level > 1) {
				// Don't include category itself, only leaf items
				childIds.push(n.wbs_library_item_id);
				childItems.push(n);
			}
			n.children.forEach(collectChildren);
		};

		collectChildren(node);

		if (childIds.length > 0) {
			selectedItems = [...new Set([...selectedItems, ...childIds])];
			onBulkSelect({ itemIds: childIds, items: childItems });
		}
	}

	function deselectAllChildren(node: WbsTreeNode) {
		const childIds: string[] = [];

		const collectChildren = (n: WbsTreeNode) => {
			childIds.push(n.wbs_library_item_id);
			n.children.forEach(collectChildren);
		};

		collectChildren(node);
		selectedItems = selectedItems.filter((id) => !childIds.includes(id));
	}

	function getSelectedChildrenCount(node: WbsTreeNode): number {
		let count = 0;

		const countSelected = (n: WbsTreeNode) => {
			if (selectedItems.includes(n.wbs_library_item_id)) count++;
			n.children.forEach(countSelected);
		};

		countSelected(node);
		return count;
	}

	function getTotalChildrenCount(node: WbsTreeNode): number {
		let count = 1; // Include the node itself
		node.children.forEach((child) => {
			count += getTotalChildrenCount(child);
		});
		return count;
	}
</script>

<div class="space-y-4">
	<!-- Search Input -->
	<div class="relative">
		<Input type="text" placeholder="Search WBS items..." bind:value={searchQuery} class="pl-10" />
		<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
			<svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
				/>
			</svg>
		</div>
	</div>

	<!-- WBS Tree -->
	<div class="max-h-96 overflow-y-auto rounded-md border">
		{#each filteredTree as node (node.wbs_library_item_id)}
			{@render wbsNode(node, 0)}
		{/each}
	</div>
</div>

{#snippet wbsNode(node: WbsTreeNode, depth: number)}
	{@const budgetTotals = aggregatedBudgets.get(node.wbs_library_item_id)}
	<div class="border-b border-gray-100 last:border-b-0">
		<div class="flex items-center p-3 hover:bg-gray-50" style="padding-left: {depth * 20 + 12}px">
			<!-- Expand/Collapse Button -->
			{#if node.children.length > 0}
				<button
					type="button"
					class="mr-2 rounded p-1 hover:bg-gray-200"
					onclick={() => toggleCategory(node.wbs_library_item_id)}
				>
					{#if expandedCategories[node.wbs_library_item_id]}
						<CaretDownIcon class="h-4 w-4 text-gray-500" />
					{:else}
						<CaretRightIcon class="h-4 w-4 text-gray-500" />
					{/if}
				</button>
			{:else}
				<div class="mr-2 w-6"></div>
			{/if}

			<!-- Checkbox for leaf items -->
			{#if node.level > 1}
				<Checkbox
					checked={selectedItems.includes(node.wbs_library_item_id)}
					onCheckedChange={(checked) => handleItemSelect(node, checked === true)}
					class="mr-3"
				/>
			{/if}

			<!-- WBS Code and Description -->
			<div class="min-w-0 flex-1">
				<div class="flex items-center space-x-2">
					<span class="font-mono text-sm font-medium">{node.code}</span>
					{#if node.level === 1}
						<Badge variant="secondary" class="text-xs">
							{getSelectedChildrenCount(node)}/{getTotalChildrenCount(node) - 1}
						</Badge>
					{/if}
				</div>
				<p class="truncate text-sm text-gray-600">{node.description}</p>

				{#if showBudgetInfo && budgetTotals?.amount}
					<p class="text-xs text-gray-500">
						Budget: {formatCurrency(budgetTotals.amount, {
							symbol: currencySymbol,
							symbolPosition,
							fallback: '-',
						})}
						{#if budgetTotals.quantity}
							• Qty: {budgetTotals.quantity}
						{/if}
					</p>
				{/if}
			</div>

			<!-- Category Actions -->
			{#if node.children.length > 0 && allowMultiSelect}
				<div class="flex space-x-1">
					<Button
						variant="ghost"
						size="sm"
						onclick={() => selectAllChildren(node)}
						class="h-auto px-2 py-1 text-xs"
					>
						Select All
					</Button>
					<Button
						variant="ghost"
						size="sm"
						onclick={() => deselectAllChildren(node)}
						class="h-auto px-2 py-1 text-xs"
					>
						Clear
					</Button>
				</div>
			{/if}
		</div>

		<!-- Children -->
		{#if expandedCategories[node.wbs_library_item_id] && node.children.length > 0}
			{#each node.children as child (child.wbs_library_item_id)}
				{@render wbsNode(child, depth + 1)}
			{/each}
		{/if}
	</div>
{/snippet}
