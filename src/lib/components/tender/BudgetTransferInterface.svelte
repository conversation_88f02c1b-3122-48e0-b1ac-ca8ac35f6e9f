<script lang="ts">
	import type { Infer, SuperValidated } from 'sveltekit-superforms';
	import { budgetTransferSchema, type WbsItemWithBudget } from '$lib/schemas/tender';
	import type { BudgetTransfer } from './budget-transfer.types';
	import BudgetTransferTable from './BudgetTransferTable.svelte';
	import BudgetTransferSankey from './BudgetTransferSankey.svelte';
	import BudgetTransferForm from './BudgetTransferForm.svelte';

	type TransferFormData = SuperValidated<Infer<typeof budgetTransferSchema>>;

	interface BaseProps {
		availableWbsItems: WbsItemWithBudget[];
		existingTransfers: BudgetTransfer[];
		currencySymbol?: string;
		symbolPosition?: 'before' | 'after';
		disabled?: boolean;
		showCreateButton?: boolean;
		showTable?: boolean;
		showSankey?: boolean;
		sankeyTitle?: string;
		sankeyDescription?: string;
	}

	type Props =
		| (BaseProps & { showForm?: true; transferForm: TransferFormData })
		| (BaseProps & { showForm: false; transferForm?: TransferFormData });

	let {
		availableWbsItems,
		existingTransfers = [],
		currencySymbol = 'kr',
		symbolPosition = 'after',
		disabled = false,
		transferForm,
		showCreateButton = true,
		showTable = true,
		showSankey = true,
		showForm = true,
		sankeyTitle,
		sankeyDescription,
	}: Props = $props();
</script>

<div class="space-y-6">
	{#if showForm}
		<BudgetTransferForm
			{availableWbsItems}
			{existingTransfers}
			{currencySymbol}
			{symbolPosition}
			{disabled}
			transferForm={transferForm!}
			{showCreateButton}
		/>
	{/if}

	{#if showTable}
		<BudgetTransferTable
			{availableWbsItems}
			{existingTransfers}
			{currencySymbol}
			{symbolPosition}
		/>
	{/if}

	{#if existingTransfers.length > 0}
		{#if showSankey}
			<BudgetTransferSankey
				{existingTransfers}
				{currencySymbol}
				{symbolPosition}
				title={sankeyTitle}
				description={sankeyDescription}
			/>
		{/if}
	{/if}
</div>
