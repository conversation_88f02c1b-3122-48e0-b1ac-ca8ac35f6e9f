<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Card from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import TrashIcon from 'phosphor-svelte/lib/Trash';
	import DotsThreeVerticalIcon from 'phosphor-svelte/lib/DotsThreeVertical';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';
	import MapIcon from 'phosphor-svelte/lib/MapTrifold';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { createLineItemSchema, editLineItemSchema } from '$lib/schemas/tender';
	import { tenderLineItemShortId } from '$lib/schemas/project';
	import { toast } from 'svelte-sonner';
	import { enhance as svelteKitEnhance } from '$app/forms';
	import ConfirmationDialog from '$lib/components/tender/ConfirmationDialog.svelte';
	import WbsMappingDialog from '$lib/components/tender/WbsMappingDialog.svelte';
	import LineItemDialog from '$lib/components/tender/LineItemDialog.svelte';
	import { formatCurrency } from '$lib/utils';
	const { data }: PageProps = $props();

	const tender = $derived(data.tender);
	const lineItems = $derived(tender?.tender_revision?.[0]?.tender_line_item || []);

	let addDialogOpen = $state(false);
	let editDialogOpen = $state(false);
	let deleteDialogOpen = $state(false);
	let wbsMappingDialogOpen = $state(false);
	let selectedLineItem = $state<(typeof lineItems)[0] | null>(null);

	// Initialize superforms for create and edit
	const form = superForm(data.form, {
		validators: zodClient(createLineItemSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					addDialogOpen = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
		delayMs: 500,
		timeoutMs: 8000,
	});

	const editForm = superForm(data.editForm, {
		validators: zodClient(editLineItemSchema),
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					editDialogOpen = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
		delayMs: 500,
		timeoutMs: 8000,
	});

	const { form: formData } = form;
	const { form: editFormData } = editForm;

	function toNumber(value: unknown) {
		if (typeof value === 'number') {
			return Number.isFinite(value) ? value : 0;
		}

		if (typeof value === 'string' && value.trim() !== '') {
			const parsed = Number(value);
			return Number.isFinite(parsed) ? parsed : 0;
		}

		return 0;
	}

	function computeAutoUnitRate({
		material_rate,
		labor_rate,
		productivity_factor,
	}: {
		material_rate?: number | null;
		labor_rate?: number | null;
		productivity_factor?: number | null;
	}) {
		const materialRate = toNumber(material_rate);
		const laborRate = toNumber(labor_rate);
		const productivity = toNumber(productivity_factor);
		const safeProductivity = productivity > 0 ? productivity : 1;

		return materialRate + laborRate / safeProductivity;
	}

	function openAddDialog() {
		// Set next line number
		const maxLineNumber = Math.max(...lineItems.map((item) => item.line_number), 0);
		$formData.line_number = maxLineNumber + 1;
		$formData.unit_rate_manual_override = false;
		$formData.primary_wbs_library_item_id = null;
		addDialogOpen = true;
	}

	function openEditDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		// Populate edit form with current values
		$editFormData.tender_line_item_id = lineItem.tender_line_item_id;
		$editFormData.line_number = lineItem.line_number;
		$editFormData.description = lineItem.description;
		$editFormData.quantity = lineItem.quantity;
		$editFormData.unit = lineItem.unit;
		$editFormData.material_rate = lineItem.material_rate;
		$editFormData.labor_rate = lineItem.labor_rate;
		$editFormData.productivity_factor = lineItem.productivity_factor;
		$editFormData.unit_rate = lineItem.unit_rate;
		$editFormData.subtotal = lineItem.subtotal;
		$editFormData.normalization_type = lineItem.normalization_type || 'amount';
		$editFormData.normalization_amount = lineItem.normalization_amount;
		$editFormData.normalization_percentage = lineItem.normalization_percentage;
		$editFormData.notes = lineItem.notes;

		const primaryMapping = (lineItem.tender_wbs_mapping ?? []).reduce(
			(best, current) => {
				const bestCoverage = best?.coverage_percentage ?? -1;
				const currentCoverage = current.coverage_percentage ?? 0;
				return currentCoverage > bestCoverage ? current : best;
			},
			null as (typeof lineItem.tender_wbs_mapping)[0] | null,
		);
		$editFormData.primary_wbs_library_item_id = primaryMapping?.wbs_library_item_id ?? null;

		const calculatedUnitRate = computeAutoUnitRate(lineItem);
		const savedUnitRate = toNumber(lineItem.unit_rate);
		const isManualOverride = Math.abs(savedUnitRate - calculatedUnitRate) > 0.0001;
		$editFormData.unit_rate_manual_override = isManualOverride;
		if (!isManualOverride) {
			$editFormData.unit_rate = calculatedUnitRate;
		}
		editDialogOpen = true;
	}

	function openDeleteDialog(lineItem: (typeof lineItems)[0]) {
		selectedLineItem = lineItem;
		deleteDialogOpen = true;
	}
</script>

<svelte:head>
	<title>Line Items - {tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold tracking-tight">Line Items</h1>
			<p class="text-muted-foreground">
				Manage line items for {tender?.tender_name}
			</p>
		</div>
		<div class="flex items-center space-x-2">
			<Button onclick={openAddDialog}>
				<PlusIcon class="mr-2 h-4 w-4" />
				Add Line Item
			</Button>
		</div>
	</div>

	<!-- Summary Card -->
	<Card.Root>
		<Card.Header>
			<Card.Title>Summary</Card.Title>
		</Card.Header>
		<Card.Content>
			<dl class="grid grid-cols-3 gap-4">
				<div>
					<dt class="text-sm font-medium text-gray-500">Total Line Items</dt>
					<dd class="text-2xl font-bold">{lineItems.length}</dd>
				</div>
				<div>
					<dt class="text-sm font-medium text-gray-500">Total Amount</dt>
					<dd class="text-2xl font-bold">
						{formatCurrency(
							lineItems.reduce((sum, item) => sum + (item.subtotal || 0), 0),
							{
								symbol: tender.currency.symbol,
								symbolPosition: tender.currency.symbol_position as 'before' | 'after',
								fallback: '-',
							},
						)}
					</dd>
				</div>
				<div>
					<dt class="text-sm font-medium text-gray-500">WBS Mappings</dt>
					<dd class="text-2xl font-bold">
						{lineItems.reduce(
							(sum: number, item) => sum + (item.tender_wbs_mapping?.length || 0),
							0,
						)}
					</dd>
				</div>
			</dl>
		</Card.Content>
	</Card.Root>

	<!-- Line Items Table -->
	{#if lineItems.length > 0}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Line #</TableHead>
						<TableHead>Description</TableHead>
						<TableHead>Quantity</TableHead>
						<TableHead>Unit</TableHead>
						<TableHead>Unit Rate</TableHead>
						<TableHead>Subtotal</TableHead>
						<TableHead>Normalization</TableHead>
						<TableHead>WBS</TableHead>
						<TableHead class="text-right">Actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each lineItems as item (item.tender_line_item_id)}
						<TableRow>
							<TableCell>{item.line_number}</TableCell>
							<TableCell class="max-w-xs">
								<div class="truncate" title={item.description}>
									{item.description}
								</div>
							</TableCell>
							<TableCell>{item.quantity || '-'}</TableCell>
							<TableCell>{item.unit || '-'}</TableCell>
							<TableCell>
								{item.unit_rate
									? formatCurrency(item.unit_rate, {
											symbol: tender.currency.symbol,
											symbolPosition: tender.currency.symbol_position as 'before' | 'after',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{item.subtotal
									? formatCurrency(item.subtotal, {
											symbol: tender.currency.symbol,
											symbolPosition: tender.currency.symbol_position as 'before' | 'after',
											fallback: '-',
										})
									: '-'}
							</TableCell>
							<TableCell>
								{#if item.normalization_type === 'percentage'}
									{item.normalization_percentage}%
								{:else if item.normalization_amount}
									{formatCurrency(item.normalization_amount, {
										symbol: tender.currency.symbol,
										symbolPosition: tender.currency.symbol_position as 'before' | 'after',
										fallback: '-',
									})}
								{:else}
									-
								{/if}
							</TableCell>
							<TableCell>
								<a
									href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
										data.client_name,
									)}/projects/{encodeURIComponent(
										data.project_id_short,
									)}/tenders/{encodeURIComponent(
										data.tender_id_short,
									)}/line-items/{tenderLineItemShortId(item.tender_line_item_id)}/wbs-mapping"
								>
									{item.tender_wbs_mapping?.length || 0}
								</a>
							</TableCell>
							<TableCell class="text-right">
								<DropdownMenu.Root>
									<DropdownMenu.Trigger>
										{#snippet child({ props })}
											<Button {...props} variant="ghost" class="size-8 p-0">
												<span class="sr-only">Open menu</span>
												<DotsThreeVerticalIcon class="size-4" />
											</Button>
										{/snippet}
									</DropdownMenu.Trigger>
									<DropdownMenu.Content align="end">
										<a
											href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
												data.client_name,
											)}/projects/{encodeURIComponent(
												data.project_id_short,
											)}/tenders/{encodeURIComponent(
												data.tender_id_short,
											)}/line-items/{tenderLineItemShortId(item.tender_line_item_id)}/wbs-mapping"
										>
											<DropdownMenu.Item>
												<MapIcon class="mr-2 h-4 w-4" />
												WBS Mapping
											</DropdownMenu.Item>
										</a>
										<DropdownMenu.Item onclick={() => openEditDialog(item)}>
											<PencilIcon class="mr-2 h-4 w-4" />
											Edit
										</DropdownMenu.Item>
										<DropdownMenu.Separator />
										<DropdownMenu.Item onclick={() => openDeleteDialog(item)}>
											<TrashIcon class="mr-2 h-4 w-4" />
											Delete
										</DropdownMenu.Item>
									</DropdownMenu.Content>
								</DropdownMenu.Root>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{:else}
		<div class="py-12 text-center">
			<h3 class="mt-2 text-sm font-semibold text-gray-900">No line items</h3>
			<p class="mt-1 text-sm text-gray-500">Get started by adding your first line item.</p>
			<div class="mt-6">
				<Button onclick={openAddDialog}>
					<PlusIcon class="mr-2 h-4 w-4" />
					Add Line Item
				</Button>
			</div>
		</div>
	{/if}
</div>

<!-- Line Item Dialogs -->
<LineItemDialog
	bind:open={addDialogOpen}
	{form}
	action="?/create"
	title="Add Line Item"
	description="Add a new line item to this tender revision."
	submitLabel="Add Line Item"
	currencySymbol={tender.currency.symbol}
	currencySymbolPosition={tender.currency.symbol_position as 'before' | 'after'}
	wbsItems={data.wbsItems}
	wbsItemsTree={data.wbsItemsTree}
/>

<LineItemDialog
	bind:open={editDialogOpen}
	form={editForm}
	action="?/update"
	title="Edit Line Item"
	description="Update the line item details."
	submitLabel="Update Line Item"
	currencySymbol={tender.currency.symbol}
	currencySymbolPosition={tender.currency.symbol_position as 'before' | 'after'}
	hiddenFields={[
		{
			name: 'tender_line_item_id',
			value: selectedLineItem?.tender_line_item_id,
		},
	]}
	wbsItems={data.wbsItems}
	wbsItemsTree={data.wbsItemsTree}
/>

<!-- Delete Confirmation Dialog -->
<ConfirmationDialog
	open={deleteDialogOpen}
	title="Delete Line Item"
	description="Are you sure you want to delete this line item? This action cannot be undone."
	confirmText="Delete"
	variant="destructive"
	onCancel={() => {
		deleteDialogOpen = false;
		selectedLineItem = null;
	}}
>
	{#snippet formContent()}
		<form
			method="POST"
			action="?/delete"
			use:svelteKitEnhance={() => {
				return ({ update }: { update: () => void }) => {
					deleteDialogOpen = false;
					selectedLineItem = null;
					update();
				};
			}}
		>
			<input type="hidden" name="line_item_id" value={selectedLineItem?.tender_line_item_id} />
			<div class="flex gap-3 pt-4">
				<Button
					variant="outline"
					type="button"
					onclick={() => {
						deleteDialogOpen = false;
						selectedLineItem = null;
					}}
				>
					Cancel
				</Button>
				<Button variant="destructive" type="submit">Delete</Button>
			</div>
		</form>
	{/snippet}
</ConfirmationDialog>

<!-- WBS Mapping Dialog -->
{#if selectedLineItem}
	<WbsMappingDialog
		bind:open={wbsMappingDialogOpen}
		lineItem={selectedLineItem}
		wbsItemsTree={data.wbsItemsTree}
		form={data.wbsMappingForm}
	/>
{/if}
