<script lang="ts">
	import type { PageProps } from './$types';
	import BudgetTransferInterface from '$lib/components/tender/BudgetTransferInterface.svelte';
	import type { WbsItemWithBudget } from '$lib/schemas/tender';

	type TenderSummary = {
		tender_id: string;
		tender_name: string | null;
	};

	type TenderBudgetTransfer = {
		budget_transfer_id: string;
		from_wbs_library_item_id: string;
		to_wbs_library_item_id: string;
		transfer_amount: number;
		reason: string | null;
		created_at: string;
		updated_at: string;
		from_wbs_item: { code: string | null; description: string | null } | null;
		to_wbs_item: { code: string | null; description: string | null } | null;
	};

	type WbsItemWithCurrency = WbsItemWithBudget & {
		currency?: {
			symbol?: string | null;
			symbol_position?: 'before' | 'after' | null;
		} | null;
	};

	const { data }: PageProps = $props();

	// Adapt RPC payloads for the BudgetTransferInterface
	const tender = $derived((data.tender as TenderSummary | null) ?? null);
	const wbsItems = $derived((data.wbsItems as WbsItemWithCurrency[] | undefined) ?? []);
	const budgetTransfers = $derived(
		((data.budgetTransfers as TenderBudgetTransfer[] | undefined) ?? []).map((transfer) => ({
			...transfer,
			reason: transfer.reason ?? 'No reason provided',
			from_wbs_item: transfer.from_wbs_item
				? {
						code: transfer.from_wbs_item.code ?? '',
						description: transfer.from_wbs_item.description ?? '',
					}
				: undefined,
			to_wbs_item: transfer.to_wbs_item
				? {
						code: transfer.to_wbs_item.code ?? '',
						description: transfer.to_wbs_item.description ?? '',
					}
				: undefined,
		})),
	);

	// Get currency information from the first WBS item (they should all have the same currency)
	const currencyInfo = $derived.by(() => {
		const firstItem = wbsItems[0];
		if (firstItem?.currency) {
			const symbol = firstItem.currency.symbol ?? 'kr';
			const symbolPosition = firstItem.currency.symbol_position === 'before' ? 'before' : 'after';
			return { symbol, symbolPosition };
		}
		return { symbol: 'kr', symbolPosition: 'after' as const };
	});
</script>

<svelte:head>
	<title>Budget Transfers - {tender?.tender_name} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<h1 class="sr-only">Budget Transfers</h1>
	<!-- Budget Transfer Interface -->
	<BudgetTransferInterface
		availableWbsItems={wbsItems}
		existingTransfers={budgetTransfers}
		currencySymbol={currencyInfo.symbol}
		symbolPosition={currencyInfo.symbolPosition as 'before' | 'after'}
		transferForm={data.transferForm}
	/>
</div>
