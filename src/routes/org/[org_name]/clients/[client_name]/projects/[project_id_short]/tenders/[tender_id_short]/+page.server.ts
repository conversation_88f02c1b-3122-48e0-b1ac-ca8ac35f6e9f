import { error, redirect } from '@sveltejs/kit';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, url, locals: { supabase, user, log } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const tenderId = tenderUUID(params.tender_id_short);
	const tabParam = url.searchParams.get('tab');
	const validTabs = new Set(['line-items', 'revisions', 'scoring', 'budget-transfers']);
	const activeTab = validTabs.has(tabParam ?? '') ? (tabParam as string) : 'line-items';

	const { data: payload, error: rpcError } = await supabase.rpc('get_tender_budget_transfer_data', {
		project_id_param: projectId,
		tender_id_param: tenderId,
	});

	if (rpcError) {
		log.error({ msg: 'Error loading tender budget transfer data', error: rpcError });
		throw error(500, 'Failed to load tender data');
	}

	if (!payload || payload.length === 0) {
		throw error(404, 'Tender not found');
	}

	const data = payload[0];

	return {
		activeTab,
		budgetTransfers: data.budget_transfers,
		wbsItems: data.wbs_items,
	};
};
