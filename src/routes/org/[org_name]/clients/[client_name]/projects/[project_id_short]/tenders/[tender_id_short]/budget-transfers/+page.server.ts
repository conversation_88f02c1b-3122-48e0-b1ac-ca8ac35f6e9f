import { error, redirect } from '@sveltejs/kit';
import { setError, fail, superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { budgetTransferSchema } from '$lib/schemas/tender';
import { tenderUUID, projectUUID } from '$lib/schemas/project';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ params, locals: { supabase, user, log } }) => {
	if (!user) {
		throw redirect(302, '/auth/signin');
	}

	const projectId = projectUUID(params.project_id_short);
	const tenderId = tenderUUID(params.tender_id_short);

	try {
		// Get tender-level budget transfer data using RPC function
		const { data: payload, error: rpcError } = await supabase.rpc(
			'get_tender_budget_transfer_data',
			{
				project_id_param: projectId,
				tender_id_param: tenderId,
			},
		);

		if (rpcError) {
			log.error({ msg: 'Error loading tender budget transfer data', error: rpcError });
			throw error(500, 'Failed to load tender data');
		}

		if (!payload || payload.length === 0) {
			throw error(404, 'Tender not found');
		}

		const data = payload[0];

		// Initialize form
		const transferForm = await superValidate(zod(budgetTransferSchema));

		return {
			tender: data.tender,
			wbsItems: data.wbs_items,
			budgetTransfers: data.budget_transfers,
			transferForm,
		};
	} catch (err) {
		log.error({ msg: 'Error loading tender budget transfer page', error: err });
		throw error(500, 'Failed to load page data');
	}
};

export const actions: Actions = {
	createTransfer: async ({ request, params, locals: { supabase, user, log } }) => {
		if (!user) {
			throw error(401, 'Unauthorized');
		}

		const projectId = projectUUID(params.project_id_short);
		const transferForm = await superValidate(request, zod(budgetTransferSchema));

		if (!transferForm.valid) {
			return fail(400, { transferForm: transferForm });
		}

		try {
			// Use RPC function for validation and creation with NULL line_item_id for tender-level
			const { data, error: rpcError } = await supabase.rpc('create_budget_transfer', {
				project_id_param: projectId,
				from_wbs_item_id: transferForm.data.from_wbs_library_item_id,
				to_wbs_item_id: transferForm.data.to_wbs_library_item_id,
				transfer_amount: transferForm.data.transfer_amount,
				reason: transferForm.data.transfer_reason || 'Budget transfer for tender analysis',
				// line_item_id_param omitted for tender-level transfers (uses DEFAULT NULL)
			});

			if (rpcError || !data?.[0]?.is_valid) {
				log.error({
					msg: `Error creating budget transfer`,
					error: rpcError || data?.[0]?.error_message,
				});
				return setError(
					transferForm,
					'transfer_amount',
					data?.[0]?.error_message || 'Failed to create budget transfer',
				);
			}

			return {
				transferForm: transferForm,
				message: { type: 'success', text: 'Budget transfer created successfully' },
			};
		} catch (err) {
			log.error({ msg: 'Error creating budget transfer:', error: err });
			return fail(500, {
				transferForm: transferForm,
				message: { type: 'error', text: 'Failed to create budget transfer' },
			});
		}
	},
};
