import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { projectUUID, tenderUUID } from '$lib/schemas/project';
import {
	createLineItemSchema,
	editLineItemSchema,
	createWbsMappingSchema,
} from '$lib/schemas/tender';
import {
	getTenderById,
	createLineItem,
	updateLineItem,
	deleteLineItem,
	createWbsMapping,
	updateWbsMapping,
} from '$lib/tender_utils';
import { message } from 'sveltekit-superforms';
import { buildWbsItemTree } from '$lib/wbs_utils';

export const load: PageServerLoad = async ({ locals, params }) => {
	await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();
	const { tender_id_short } = params;

	if (!tender_id_short) {
		throw error(400, 'Tender ID is required');
	}

	const project_id = projectUUID(project_id_short);
	const tender_id = tenderUUID(tender_id_short);

	// Fetch tender details with line items
	const tender = await getTenderById(supabase, tender_id);

	// Verify the tender belongs to the project
	if (tender.project_id !== project_id) {
		throw error(404, 'Tender not found');
	}

	// Fetch project details to get WBS library
	const { data: project, error: projectError } = await supabase
		.from('project')
		.select('wbs_library_id, client_id, active_budget_version_id')
		.eq('project_id', project_id)
		.single();

	if (projectError || !project) {
		throw error(500, 'Failed to load project details');
	}

	// Fetch WBS items for this project (standard + client/project custom)
	const orFilter = [
		`and(wbs_library_id.eq.${project.wbs_library_id},item_type.eq.Standard)`,
		`and(client_id.eq.${project.client_id},item_type.eq.Custom,project_id.is.null)`,
		`and(client_id.eq.${project.client_id},item_type.eq.Custom,project_id.eq.${project_id})`,
	].join(',');

	const { data: wbsItems, error: wbsItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.or(orFilter)
		.order('code');

	if (wbsItemsError) {
		throw error(500, 'Failed to load WBS items');
	}

	// Fetch budget values for the active budget version if available
	const activeBudgetVersionId = project.active_budget_version_id;
	let budgetItems: {
		wbs_library_item_id: string;
		quantity: number | null;
		unit_rate: number | null;
		factor: number | null;
		unit: string | null;
	}[] = [];

	if (activeBudgetVersionId) {
		const { data: budgetVersionItems, error: budgetItemsError } = await supabase
			.from('budget_version_item')
			.select('wbs_library_item_id, quantity, unit_rate, factor, unit')
			.eq('budget_version_id', activeBudgetVersionId);

		if (budgetItemsError) {
			throw error(500, 'Failed to load budget data');
		}

		budgetItems = budgetVersionItems ?? [];
	}

	const budgetMap = new Map<string, (typeof budgetItems)[number]>();
	budgetItems.forEach((item) => {
		budgetMap.set(item.wbs_library_item_id, item);
	});

	const enrichedWbsItems = (wbsItems ?? []).map((item) => {
		const budget = budgetMap.get(item.wbs_library_item_id);
		const quantity = budget?.quantity ?? null;
		const unitRate = budget?.unit_rate ?? null;
		const factor = budget?.factor ?? null;
		const budgetAmount =
			quantity !== null && unitRate !== null
				? Number(quantity) * Number(unitRate) * Number(factor ?? 1)
				: null;

		return {
			...item,
			budget_amount: budgetAmount ?? undefined,
			budget_quantity: quantity,
			budget_unit_rate: unitRate,
			unit: budget?.unit ?? null,
		};
	});

	// Build hierarchical tree structure
	const wbsItemsTree = buildWbsItemTree(enrichedWbsItems);

	// Initialize forms for creating and editing line items and WBS mappings
	const form = await superValidate(zod(createLineItemSchema));
	const editForm = await superValidate(zod(editLineItemSchema));
	const wbsMappingForm = await superValidate(zod(createWbsMappingSchema));

	return {
		tender,
		tender_id_short,
		wbsItems: enrichedWbsItems,
		wbsItemsTree,
		form,
		editForm,
		wbsMappingForm,
	};
};

export const actions: Actions = {
	create: async ({ request, locals, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const tender_id = tenderUUID(tender_id_short);

		// Validate form data with superforms
		const form = await superValidate(request, zod(createLineItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			const selectedPrimaryWbs = form.data.primary_wbs_library_item_id ?? null;
			// TODO: turn this into an RPC
			// Get current revision
			const { data: currentRevision, error: revisionError } = await supabase
				.from('tender_revision')
				.select('tender_revision_id')
				.eq('tender_id', tender_id)
				.eq('is_current', true)
				.single();

			if (revisionError || !currentRevision) {
				return fail(500, {
					form,
					message: { type: 'error', text: 'Failed to find current tender revision' },
				});
			}

			const lineItemData = {
				tender_revision_id: currentRevision.tender_revision_id,
				...form.data,
			};

			const createdLineItem = await createLineItem(supabase, lineItemData);

			if (selectedPrimaryWbs) {
				await createWbsMapping(supabase, createdLineItem.tender_line_item_id, {
					wbs_library_item_id: selectedPrimaryWbs,
					coverage_percentage: 100,
					coverage_quantity: typeof form.data.quantity === 'number' ? form.data.quantity : null,
				});
			}
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error creating line item:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create line item' },
			});
		}
		return message(form, {
			type: 'success',
			text: `Line item has been added successfully.`,
		});
	},

	update: async ({ request, locals, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		// Validate form data with superforms
		const form = await superValidate(request, zod(editLineItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		const lineItemId = form.data.tender_line_item_id;

		if (!lineItemId) {
			return fail(400, {
				form,
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		try {
			const selectedPrimaryWbs = form.data.primary_wbs_library_item_id ?? null;
			await updateLineItem(supabase, lineItemId, form.data);

			if (selectedPrimaryWbs) {
				const { data: existingMappings, error: existingMappingsError } = await supabase
					.from('tender_wbs_mapping')
					.select(
						'tender_wbs_mapping_id, wbs_library_item_id, coverage_percentage, coverage_quantity',
					)
					.eq('tender_line_item_id', lineItemId);

				if (existingMappingsError) {
					return fail(500, {
						form,
						message: { type: 'error', text: 'Failed to update primary WBS mapping' },
					});
				}

				const matchingMapping = existingMappings?.find(
					(mapping) => mapping.wbs_library_item_id === selectedPrimaryWbs,
				);

				if (matchingMapping) {
					await updateWbsMapping(supabase, matchingMapping.tender_wbs_mapping_id, {
						coverage_percentage: 100,
						coverage_quantity: typeof form.data.quantity === 'number' ? form.data.quantity : null,
					});
				} else {
					await createWbsMapping(supabase, lineItemId, {
						wbs_library_item_id: selectedPrimaryWbs,
						coverage_percentage: 100,
						coverage_quantity: typeof form.data.quantity === 'number' ? form.data.quantity : null,
					});
				}
			}
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error updating line item:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update line item' },
			});
		}
		return message(form, {
			type: 'success',
			text: `Line item has been updated successfully.`,
		});
	},

	createWbsMapping: async ({ request, locals, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		// Get form data first
		const formData = await request.formData();
		const lineItemId = formData.get('line_item_id') as string;

		if (!lineItemId) {
			return fail(400, {
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		// Validate form data with superforms
		const form = await superValidate(formData, zod(createWbsMappingSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await createWbsMapping(supabase, lineItemId, form.data);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error creating WBS mapping:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create WBS mapping' },
			});
		}
		return message(form, {
			type: 'success',
			text: `WBS mapping has been created successfully.`,
		});
	},

	delete: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();
		const { tender_id_short } = params;

		if (!tender_id_short) {
			throw error(400, 'Tender ID is required');
		}

		const formData = await request.formData();
		const lineItemId = formData.get('line_item_id') as string;

		if (!lineItemId) {
			return fail(400, {
				message: { type: 'error', text: 'Line item ID is required' },
			});
		}

		try {
			await deleteLineItem(supabase, lineItemId);

			throw redirect(
				302,
				`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
					client_name,
				)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
					tender_id_short,
				)}/line-items`,
				{
					type: 'success',
					message: 'Line item has been deleted successfully.',
				},
				cookies,
			);
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error deleting line item:', err });
			return fail(500, {
				message: { type: 'error', text: 'Failed to delete line item' },
			});
		}
	},
};
