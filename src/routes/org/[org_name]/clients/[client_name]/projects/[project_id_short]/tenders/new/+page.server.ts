import type { Actions, PageServerLoad } from './$types';
import { requireUser, requireProject } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import { error, fail } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { tenderSchema, type TenderRow } from '$lib/schemas/tender';
import { vendorSchema } from '$lib/schemas/vendor';
import { projectUUID, tenderShortId } from '$lib/schemas/project';
import { createTender } from '$lib/tender_utils';

import { today, getLocalTimeZone } from '@internationalized/date';
import { createVendorModal } from '$lib/components/forms/vendor/vendor_form_actions';

export const load: PageServerLoad = async ({ locals }) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { project_id_short } = requireProject();

	const project_id = projectUUID(project_id_short);

	// Fetch vendors for the project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: 'project',
		entity_id_param: project_id,
	});

	if (vendorsError) {
		locals.log.error({ msg: 'Error fetching vendors:', vendorsError });
		throw error(500, 'Failed to fetch vendors');
	}

	// Fetch available currencies
	const { data: currencies, error: currenciesError } = await supabase
		.from('currency')
		.select('currency_code, symbol, symbol_position, description')
		.order('currency_code');

	if (currenciesError) {
		locals.log.error({ msg: 'Error fetching currencies:', currenciesError });
		throw error(500, 'Failed to fetch currencies');
	}

	// Initialize form with default values
	const form = await superValidate(
		{
			tender_name: '',
			description: '',
			vendor_id: '',
			submission_date: today(getLocalTimeZone()).toString(),
			currency_code: 'SEK',
			status: 'submitted',
			notes: '',
		},
		zod(tenderSchema),
		{ errors: false },
	);

	// Initialize vendor form for modal
	const newVendorForm = await superValidate(
		{
			name: '',
			description: '',
			vendor_type: null,
			contact_name: '',
			contact_email: '',
			contact_phone: '',
			currency: 'SEK',
			payment_terms: null,
			payment_terms_days: null,
			is_active: true,
			org_id: null,
			client_id: null,
			project_id: project_id,
		},
		zod(vendorSchema),
		{ errors: false },
	);

	return {
		form,
		newVendorForm,
		vendors: vendors || [],
		currencies: currencies || [],
		project: {
			project_id,
			name: 'Project Name', // This should come from the project data
			client: {
				client_id: 'client_id', // This should come from the project data
				name: 'Client Name', // This should come from the project data
			},
		},
	};
};

export const actions: Actions = {
	createVendorModal,

	createTender: async ({ request, locals, cookies }) => {
		await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_id_short } = requireProject();

		const project_id = projectUUID(project_id_short);

		const form = await superValidate(request, zod(tenderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		let tender: TenderRow;

		try {
			// Create the tender
			tender = await createTender(supabase, {
				tender_name: form.data.tender_name,
				description: form.data.description,
				vendor_id: form.data.vendor_id,
				submission_date: form.data.submission_date,
				currency_code: form.data.currency_code,
				status: form.data.status,
				notes: form.data.notes,
				project_id,
			});
		} catch (err) {
			if (err instanceof Response) {
				throw err;
			}
			locals.log.error({ msg: 'Error creating tender:', err });
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create tender' },
			});
		}
		const tender_id_short = tenderShortId(tender.tender_id);

		throw redirect(
			302,
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(
				client_name,
			)}/projects/${encodeURIComponent(project_id_short)}/tenders/${encodeURIComponent(
				tender_id_short,
			)}`,
			{
				type: 'success',
				message: `Tender "${tender.tender_name}" has been created successfully.`,
			},
			cookies,
		);
	},
};
