<script lang="ts">
	import type { PageProps } from './$types';
	import { Button } from '$lib/components/ui/button';
	import * as Form from '$lib/components/ui/form';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import { Calendar } from '$lib/components/ui/calendar';
	import * as Popover from '$lib/components/ui/popover';
	import * as Dialog from '$lib/components/ui/dialog';
	import CalendarIcon from 'phosphor-svelte/lib/Calendar';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { tenderSchema } from '$lib/schemas/tender';
	import NewVendor from '$lib/components/forms/vendor/NewVendor.svelte';
	import type { VendorListItem } from '$lib/schemas/vendor';
	import {
		DateFormatter,
		getLocalTimeZone,
		parseDate,
		today,
		type DateValue,
	} from '@internationalized/date';

	const { data }: PageProps = $props();

	const form = superForm(data.form, {
		validators: zodClient(tenderSchema),
	});

	const { form: formData, enhance } = form;

	const df = new DateFormatter('en-US', {
		dateStyle: 'long',
	});

	let calendarOpen = $state(false);
	let vendorDialogOpen = $state(false);
	let submissionDateValue = $derived(
		$formData.submission_date ? parseDate($formData.submission_date) : undefined,
	);
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));

	// Vendor list that can be updated when new vendor is created
	let vendorList = $state<typeof data.vendors>([...data.vendors]);

	// Convert vendors to select options
	const vendorOptions = $derived(
		vendorList.map((vendor) => ({
			value: vendor.vendor_id,
			label: vendor.name,
		})),
	);

	// Handle vendor creation from modal
	function handleVendorCreated(vendor: VendorListItem | null) {
		if (vendor) {
			// Convert VendorListItem to match the existing vendor list format
			const vendorForList = {
				vendor_id: vendor.vendor_id,
				name: vendor.name,
				description: vendor.description || '',
				vendor_type: vendor.vendor_type || '',
				contact_name: vendor.contact_name || '',
				contact_email: vendor.contact_email || '',
				contact_phone: vendor.contact_phone || '',
				is_active: vendor.is_active,
				access_level: vendor.access_level as string,
			};
			// Add the new vendor to the list
			vendorList = [...vendorList, vendorForList];
			// Auto-select the new vendor
			$formData.vendor_id = vendor.vendor_id;
			// Close the dialog
			vendorDialogOpen = false;
		}
	}

	// Convert currencies to select options
	const currencyOptions = $derived(
		data.currencies.map((currency) => ({
			value: currency.currency_code,
			label: `${currency.currency_code} (${currency.symbol})`,
		})),
	);

	// Status options
	const statusOptions = [
		{ value: 'submitted', label: 'Submitted' },
		{ value: 'under_review', label: 'Under Review' },
		{ value: 'selected', label: 'Selected' },
		{ value: 'rejected', label: 'Rejected' },
	];
</script>

<svelte:head>
	<title>New Tender - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<div>
		<h1 class="text-2xl font-bold tracking-tight">Create New Tender</h1>
		<p class="text-muted-foreground">
			Add a new tender submission for {data.project?.name}
		</p>
	</div>

	<form method="POST" use:enhance class="space-y-6" action="?/createTender">
		<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
			<Form.Field {form} name="tender_name">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Tender Name <span class="text-red-500">*</span></Form.Label>
						<Input {...props} bind:value={$formData.tender_name} placeholder="Enter tender name" />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="vendor_id">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Vendor <span class="text-red-500">*</span></Form.Label>
						<div class="flex items-center gap-2">
							{#if vendorOptions.length > 0}
								<div class="">
									<Select.Root type="single" bind:value={$formData.vendor_id} name={props.name}>
										<Select.Trigger {...props}>
											{$formData.vendor_id
												? vendorOptions.find((v) => v.value === $formData.vendor_id)?.label
												: 'Select a vendor'}
										</Select.Trigger>
										<Select.Content>
											{#each vendorOptions as vendor (vendor.value)}
												<Select.Item value={vendor.value}>
													{vendor.label}
												</Select.Item>
											{/each}
										</Select.Content>
									</Select.Root>
								</div>
								<p class="text-muted-foreground shrink-0 text-center text-sm">or</p>
							{:else}
								<p class="text-muted-foreground flex-1 text-sm">No vendors available.</p>
							{/if}
							<Button
								type="button"
								variant="secondary"
								size="sm"
								onclick={() => {
									vendorDialogOpen = true;
								}}
							>
								<PlusIcon class="mr-2 h-4 w-4" />
								Add New Vendor
							</Button>
						</div>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="submission_date">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Submission Date <span class="text-red-500">*</span></Form.Label>
						<Popover.Root bind:open={calendarOpen}>
							<Popover.Trigger>
								{#snippet child({ props: triggerProps })}
									<Button
										{...props}
										{...triggerProps}
										variant="outline"
										class="w-full justify-start text-left font-normal"
									>
										<CalendarIcon class="mr-2 h-4 w-4" />
										{submissionDateValue
											? df.format(submissionDateValue.toDate(getLocalTimeZone()))
											: 'Pick a date'}
									</Button>
								{/snippet}
							</Popover.Trigger>
							<Popover.Content class="w-auto p-0" align="start">
								<Calendar
									type="single"
									value={submissionDateValue as DateValue}
									bind:placeholder
									captionLayout="dropdown"
									onValueChange={(v) => {
										if (v) {
											$formData.submission_date = v.toString();
										} else {
											$formData.submission_date = '';
										}
										calendarOpen = false;
									}}
								/>
							</Popover.Content>
						</Popover.Root>
						<input type="hidden" name="submission_date" bind:value={$formData.submission_date} />
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="currency_code">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Currency <span class="text-red-500">*</span></Form.Label>
						<Select.Root type="single" bind:value={$formData.currency_code} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.currency_code
									? currencyOptions.find((c) => c.value === $formData.currency_code)?.label
									: 'Select currency'}
							</Select.Trigger>
							<Select.Content>
								{#each currencyOptions as currency (currency.value)}
									<Select.Item value={currency.value}>
										{currency.label}
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="status">
				<Form.Control>
					{#snippet children({ props })}
						<Form.Label>Status <span class="text-red-500">*</span></Form.Label>
						<Select.Root type="single" bind:value={$formData.status} name={props.name}>
							<Select.Trigger {...props}>
								{$formData.status
									? statusOptions.find((s) => s.value === $formData.status)?.label
									: 'Select status'}
							</Select.Trigger>
							<Select.Content>
								{#each statusOptions as status (status.value)}
									<Select.Item value={status.value}>
										{status.label}
									</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					{/snippet}
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>
		</div>

		<Form.Field {form} name="description">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Description</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.description}
						placeholder="Enter tender description"
						rows={3}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors />
		</Form.Field>

		<Form.Field {form} name="notes">
			<Form.Control>
				{#snippet children({ props })}
					<Form.Label>Notes</Form.Label>
					<Textarea
						{...props}
						bind:value={$formData.notes}
						placeholder="Enter any additional notes"
						rows={3}
					/>
				{/snippet}
			</Form.Control>
			<Form.FieldErrors />
		</Form.Field>

		<div class="flex justify-end space-x-4">
			<Button
				type="button"
				variant="outline"
				href="/org/{encodeURIComponent(data.org_name)}/clients/{encodeURIComponent(
					data.client_name,
				)}/projects/{encodeURIComponent(data.project_id_short)}/tenders"
			>
				Cancel
			</Button>
			<Button type="submit">Create Tender</Button>
		</div>
	</form>
</div>

<!-- New Vendor Dialog -->
{#if data.newVendorForm}
	<Dialog.Root bind:open={vendorDialogOpen}>
		<Dialog.Content class="max-h-[90vh] overflow-auto">
			<NewVendor
				isModal={true}
				onVendorCreated={handleVendorCreated}
				data={{
					form: data.newVendorForm,
					organizations: [],
					clients: [
						{
							client: data.project.client,
							role: 'editor',
						},
					],
					projects: [
						{
							project: {
								project_id: data.project.project_id,
								name: data.project.name,
								client: data.project.client,
							},
							role: 'editor',
						},
					],
					currencies: data.currencies,
				}}
			/>
		</Dialog.Content>
	</Dialog.Root>
{/if}
