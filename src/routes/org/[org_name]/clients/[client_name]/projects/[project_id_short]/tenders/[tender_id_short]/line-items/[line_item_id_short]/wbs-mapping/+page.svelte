<script lang="ts">
	import type { PageProps } from './$types';
	import * as Card from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import LineItemDialog from '$lib/components/tender/LineItemDialog.svelte';
	import WbsMappingInterface from '$lib/components/tender/WbsMappingInterface.svelte';
	import { toast } from 'svelte-sonner';
	import { formatCurrency } from '$lib/utils';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zod4Client as zodClient } from 'sveltekit-superforms/adapters';
	import { editLineItemSchema } from '$lib/schemas/tender';
	import type {
		CreateWbsMappingForm,
		DeleteWbsMappingForm,
		EditWbsMappingForm,
		BulkWbsMappingForm,
		WbsMappingFormMessage,
	} from '$lib/types/wbs-mapping';
	import PencilIcon from 'phosphor-svelte/lib/Pencil';
	import { SvelteMap } from 'svelte/reactivity';
	import { buildAggregatedBudgetMap } from '$lib/wbs_utils';

	const { data }: PageProps = $props();

	const lineItem = $derived(data.lineItem);
	const wbsItems = $derived(data.wbsItems || []);
	const existingMappings = $derived(data.lineItem.tender_wbs_mapping || []);

	let editLineItemDialogOpen = $state(false);
	let editMappingDialogOpen = $state(false);

	const lineItemEditForm = superForm(data.lineItemEditForm, {
		validators: zodClient(editLineItemSchema),
		onUpdated: ({ form }) => {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					editLineItemDialogOpen = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
		delayMs: 500,
		timeoutMs: 8000,
	});

	const { form: editLineItemFormData } = lineItemEditForm;

	function toNumber(value: unknown) {
		if (typeof value === 'number') {
			return Number.isFinite(value) ? value : 0;
		}

		if (typeof value === 'string' && value.trim() !== '') {
			const parsed = Number(value);
			return Number.isFinite(parsed) ? parsed : 0;
		}

		return 0;
	}

	function computeAutoUnitRate({
		material_rate,
		labor_rate,
		productivity_factor,
	}: {
		material_rate?: number | null;
		labor_rate?: number | null;
		productivity_factor?: number | null;
	}) {
		const materialRate = toNumber(material_rate);
		const laborRate = toNumber(labor_rate);
		const productivity = toNumber(productivity_factor);
		const safeProductivity = productivity > 0 ? productivity : 1;

		return materialRate + laborRate / safeProductivity;
	}

	function openEditLineItemDialog() {
		if (!lineItem) {
			return;
		}

		$editLineItemFormData.tender_line_item_id = lineItem.tender_line_item_id;
		$editLineItemFormData.line_number = lineItem.line_number;
		$editLineItemFormData.description = lineItem.description;
		$editLineItemFormData.quantity = lineItem.quantity;
		$editLineItemFormData.unit = lineItem.unit;
		$editLineItemFormData.material_rate = lineItem.material_rate;
		$editLineItemFormData.labor_rate = lineItem.labor_rate;
		$editLineItemFormData.productivity_factor = lineItem.productivity_factor;
		$editLineItemFormData.unit_rate = lineItem.unit_rate;
		$editLineItemFormData.subtotal = lineItem.subtotal;
		$editLineItemFormData.normalization_type = lineItem.normalization_type || 'amount';
		$editLineItemFormData.normalization_amount = lineItem.normalization_amount;
		$editLineItemFormData.normalization_percentage = lineItem.normalization_percentage;
		$editLineItemFormData.notes = lineItem.notes;

		const calculatedUnitRate = computeAutoUnitRate(lineItem);
		const savedUnitRate = toNumber(lineItem.unit_rate);
		const isManualOverride = Math.abs(savedUnitRate - calculatedUnitRate) > 0.0001;
		$editLineItemFormData.unit_rate_manual_override = isManualOverride;
		if (!isManualOverride) {
			$editLineItemFormData.unit_rate = calculatedUnitRate;
		}

		editLineItemDialogOpen = true;
	}

	// Calculate mapping coverage statistics
	const mappingStats = $derived.by(() => {
		const totalMappings = existingMappings.length;
		const totalCoverage = existingMappings.reduce(
			(sum: number, mapping) => sum + (mapping.coverage_percentage || 0),
			0,
		);
		const averageCoverage = totalMappings > 0 ? totalCoverage / totalMappings : 0;

		return {
			totalMappings,
			totalCoverage,
			averageCoverage,
		};
	});

	// Get currency info from tender (fallback to default)
	const currencyInfo = $derived.by(() => ({
		symbol: 'kr',
		symbolPosition: 'after' as 'before' | 'after',
	}));

	type BudgetDifferenceStatus = 'over' | 'under' | 'balanced' | 'unknown';

	const wbsItemsById = $derived.by(() => {
		const map = new SvelteMap<string, (typeof wbsItems)[number]>();
		wbsItems.forEach((item) => {
			map.set(item.wbs_library_item_id, item);
		});
		return map;
	});

	const aggregatedBudgets = $derived.by(() => buildAggregatedBudgetMap(wbsItems));

	const budgetCoverageSummary = $derived.by(() => {
		let total = 0;
		let hasBudget = false;

		existingMappings.forEach((mapping) => {
			const wbsItem = wbsItemsById.get(mapping.wbs_library_item_id);
			if (!wbsItem) {
				return;
			}

			const budgetTotals = aggregatedBudgets.get(mapping.wbs_library_item_id);
			const budgetAmount = Number(budgetTotals?.amount ?? wbsItem.budget_amount ?? 0);
			if (!budgetAmount) {
				return;
			}

			hasBudget = true;
			const coveragePercentage =
				mapping.coverage_percentage !== null && mapping.coverage_percentage !== undefined
					? Number(mapping.coverage_percentage)
					: null;
			const coverageQuantity =
				mapping.coverage_quantity !== null && mapping.coverage_quantity !== undefined
					? Number(mapping.coverage_quantity)
					: null;
			const budgetQuantity = Number(budgetTotals?.quantity ?? wbsItem.budget_quantity ?? 0);

			if (coveragePercentage !== null) {
				total += (budgetAmount * coveragePercentage) / 100;
				return;
			}

			if (coverageQuantity !== null && budgetQuantity > 0) {
				total += (budgetAmount * coverageQuantity) / budgetQuantity;
				return;
			}

			// Default to full budget if no coverage metadata is provided
			total += budgetAmount;
		});

		return { total, hasBudget };
	});

	const budgetDifference = $derived.by(() => {
		const subtotal = Number(lineItem?.subtotal ?? 0);
		return subtotal - budgetCoverageSummary.total;
	});

	const budgetDifferenceStatus = $derived.by((): BudgetDifferenceStatus => {
		if (!budgetCoverageSummary.hasBudget) {
			return 'unknown';
		}

		const diff = budgetDifference;
		if (Number.isNaN(diff)) {
			return 'unknown';
		}

		if (Math.abs(diff) < 0.005) {
			return 'balanced';
		}

		return diff > 0 ? 'over' : 'under';
	});

	const budgetDifferenceTextClass = $derived.by(() => {
		switch (budgetDifferenceStatus) {
			case 'over':
				return 'text-red-600';
			case 'under':
				return 'text-emerald-600';
			case 'balanced':
				return 'text-gray-600';
			default:
				return 'text-gray-500';
		}
	});

	const budgetDifferenceBadgeVariant = $derived.by(() => {
		switch (budgetDifferenceStatus) {
			case 'over':
				return 'destructive' as const;
			case 'under':
				return 'secondary' as const;
			case 'balanced':
				return 'outline' as const;
			default:
				return 'outline' as const;
		}
	});

	const budgetDifferenceLabel = $derived.by(() => {
		switch (budgetDifferenceStatus) {
			case 'over':
				return 'Over Budget';
			case 'under':
				return 'Under Budget';
			case 'balanced':
				return 'On Budget';
			default:
				return 'Budget Unavailable';
		}
	});

	const createFormHandler = superForm<CreateWbsMappingForm, WbsMappingFormMessage>(
		data.createForm as SuperValidated<CreateWbsMappingForm, WbsMappingFormMessage>,
		{
			resetForm: true,
			onUpdated: ({ form }) => {
				if (form.message) {
					if (form.message.type === 'success') {
						toast.success(form.message.text);
					} else if (form.message.type === 'error') {
						toast.error(form.message.text);
					}
				}
			},
		},
	);

	const editFormHandler = superForm<EditWbsMappingForm, WbsMappingFormMessage>(
		data.editForm as SuperValidated<EditWbsMappingForm, WbsMappingFormMessage>,
		{
			resetForm: false,
			onUpdated: ({ form }) => {
				if (form.message) {
					if (form.message.type === 'success') {
						toast.success(form.message.text);
						editMappingDialogOpen = false;
					} else if (form.message.type === 'error') {
						toast.error(form.message.text);
					}
				}
			},
		},
	);

	const deleteFormHandler = superForm<DeleteWbsMappingForm, WbsMappingFormMessage>(
		data.deleteForm as SuperValidated<DeleteWbsMappingForm, WbsMappingFormMessage>,
		{
			resetForm: false,
			warnings: { duplicateId: false },
			onUpdated: ({ form }) => {
				if (form.message) {
					if (form.message.type === 'success') {
						toast.success(form.message.text);
					} else if (form.message.type === 'error') {
						toast.error(form.message.text);
					}
				}
			},
		},
	);

	const bulkFormHandler = superForm<BulkWbsMappingForm, WbsMappingFormMessage>(
		data.bulkForm as SuperValidated<BulkWbsMappingForm, WbsMappingFormMessage>,
		{
			resetForm: true,
			onUpdated: ({ form }) => {
				if (form.message) {
					if (form.message.type === 'success') {
						toast.success(form.message.text);
					} else if (form.message.type === 'error') {
						toast.error(form.message.text);
					}
				}
			},
		},
	);
</script>

<svelte:head>
	<title>WBS Mapping - {lineItem?.description} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<h1 class="sr-only">WBS Mapping</h1>

	<!-- Line Item Summary -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center justify-between">
				<span class="text-2xl">Line Item Details</span>
				<div class="flex items-center gap-3">
					<Badge variant="outline">#{lineItem?.line_number}</Badge>
					<div class="flex justify-start md:col-span-4 md:justify-end">
						<Button variant="ghost" onclick={openEditLineItemDialog} title="Edit Line Item">
							<PencilIcon class="h-4 w-4" />
							<span class="sr-only"> Edit Line Item </span>
						</Button>
					</div>
				</div>
			</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-4">
				<div class="col-span-2 text-center">
					<div class="text-2xl font-bold">
						{lineItem?.description || 'No description'}
					</div>
					<div class="text-sm text-gray-500">Description</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">
						{#if lineItem?.quantity || lineItem?.unit}
							{lineItem.quantity} {lineItem.unit}
						{:else}
							<span class="text-gray-400">-</span>
						{/if}
					</div>
					<div class="text-sm text-gray-500">Quantity & Unit</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">
						{#if lineItem?.subtotal}
							{formatCurrency(lineItem.subtotal, {
								symbol: currencyInfo.symbol,
								symbolPosition: currencyInfo.symbolPosition,
								fallback: '-',
							})}
						{:else}
							<span class="text-gray-400">-</span>
						{/if}
					</div>
					<div class="text-sm text-gray-500">Subtotal</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Mapping Statistics -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="text-base">Mapping Overview</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-4">
				<div class="text-center">
					<div class="text-2xl font-bold">{mappingStats.totalMappings}</div>
					<div class="text-sm text-gray-500">WBS Mappings</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">{mappingStats.totalCoverage.toFixed(1)}%</div>
					<div class="text-sm text-gray-500">Total Coverage</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">
						{#if budgetCoverageSummary.hasBudget}
							{formatCurrency(budgetCoverageSummary.total, {
								symbol: currencyInfo.symbol,
								symbolPosition: currencyInfo.symbolPosition,
								fallback: '-',
							})}
						{:else}
							<span class="text-gray-400">-</span>
						{/if}
					</div>
					<div class="text-sm text-gray-500">Mapped Budget</div>
				</div>
				<div class="text-center">
					{#if budgetCoverageSummary.hasBudget}
						<div class={`text-2xl font-bold ${budgetDifferenceTextClass}`}>
							{formatCurrency(budgetDifference, {
								symbol: currencyInfo.symbol,
								symbolPosition: currencyInfo.symbolPosition,
								fallback: '-',
							})}
						</div>
						<div class="text-sm text-gray-500">Budget Difference</div>
						<Badge class="mt-2" variant={budgetDifferenceBadgeVariant}
							>{budgetDifferenceLabel}</Badge
						>
					{:else}
						<div class="text-2xl font-bold text-gray-400">-</div>
						<div class="text-sm text-gray-500">Budget Difference</div>
					{/if}
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- WBS Mapping Interface -->
	<WbsMappingInterface
		bind:open={editMappingDialogOpen}
		lineItemId={lineItem?.tender_line_item_id || ''}
		lineItemDescription={lineItem?.description || ''}
		availableWbsItems={wbsItems}
		{existingMappings}
		currencySymbol={currencyInfo.symbol}
		symbolPosition={currencyInfo.symbolPosition}
		{createFormHandler}
		{editFormHandler}
		{deleteFormHandler}
		{bulkFormHandler}
	/>

	<LineItemDialog
		bind:open={editLineItemDialogOpen}
		form={lineItemEditForm}
		action="?/updateLineItem"
		title="Edit Line Item"
		description="Update the line item details."
		submitLabel="Update Line Item"
		currencySymbol={currencyInfo.symbol}
		currencySymbolPosition={currencyInfo.symbolPosition}
		hiddenFields={[
			{
				name: 'tender_line_item_id',
				value: lineItem?.tender_line_item_id,
			},
		]}
	/>
</div>
